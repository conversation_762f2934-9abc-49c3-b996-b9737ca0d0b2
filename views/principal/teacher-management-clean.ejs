<!-- Faculty Details Page -->
<div class="container mx-auto px-4 py-6">
    <div class="bg-white rounded-lg shadow-lg">
        <!-- Header -->
        <div class="bg-gray-100 p-4 rounded-t-lg border-b">
            <div class="flex justify-between items-center">
                <h2 class="text-xl font-semibold text-gray-800">Faculty Details</h2>
                <div class="flex space-x-2">
                    <input type="text" id="search-teachers" placeholder="Search teachers..." 
                           class="px-3 py-2 border border-gray-300 rounded-md text-sm">
                    <select id="filter-completion" class="px-3 py-2 border border-gray-300 rounded-md text-sm">
                        <option value="">All Profiles</option>
                        <option value="complete">Complete (≥80%)</option>
                        <option value="incomplete">Incomplete (<80%)</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Table -->
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teacher</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Experience</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Login</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Profile Completion</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200" id="teachers-table-body">
                    <% if (teachers && teachers.length > 0) { %>
                        <% teachers.forEach(teacher => { %>
                            <%
                                // Simplified profile completion calculation
                                let completedFields = 0;
                                let totalFields = 23;

                                // Count completed basic fields
                                if (teacher.name || teacher.full_name) completedFields++;
                                if (teacher.email) completedFields++;
                                if (teacher.username) completedFields++;
                                if (teacher.date_of_birth) completedFields++;
                                if (teacher.gender) completedFields++;
                                if (teacher.phone) completedFields++;
                                if (teacher.employee_id) completedFields++;
                                if (teacher.bio) completedFields++;

                                // Count completed professional fields
                                if (teacher.designation) completedFields++;
                                if (teacher.department) completedFields++;
                                if (teacher.joining_date) completedFields++;
                                if (teacher.employment_type) completedFields++;
                                if (teacher.total_experience_years) completedFields++;
                                if (teacher.emergency_contact) completedFields++;
                                if (teacher.address) completedFields++;
                                if (teacher.office_location) completedFields++;
                                if (teacher.languages_known) completedFields++;
                                if (teacher.subjects || teacher.subjects_taught) completedFields++;

                                // Estimate enhanced fields
                                if (completedFields > 10) {
                                    completedFields += 2;
                                } else if (completedFields > 5) {
                                    completedFields += 1;
                                }

                                // Calculate completion percentage
                                let profileCompletion = Math.round((completedFields / totalFields) * 100);
                                if (profileCompletion > 100) profileCompletion = 100;

                                // Determine colors
                                const progressBarColor = profileCompletion >= 80 ? '#3b82f6' : '#6b7280';
                                const textColor = profileCompletion >= 80 ? 'text-blue-600' : 'text-gray-600';
                                const profileLevel = profileCompletion >= 80 ? 'complete' : 'incomplete';
                            %>
                            <tr class="hover:bg-gray-50 teacher-row"
                                data-teacher-id="<%= teacher.id %>"
                                data-name="<%= (teacher.name || '').toLowerCase() %>"
                                data-email="<%= (teacher.email || '').toLowerCase() %>"
                                data-profile-completion="<%= profileLevel %>">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center">
                                                <span class="text-sm font-medium">
                                                    <%= (teacher.name || 'U').split(' ').map(n => n[0]).join('').toUpperCase() %>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">
                                                <%= teacher.name || 'Unknown Teacher' %>
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                <%= teacher.email || 'No email' %>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <span class="font-medium"><%= teacher.department || 'Academic' %></span>
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        <%= teacher.designation || 'Teacher' %>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <span class="font-medium"><%= teacher.total_experience_years || '0' %></span> years
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        Total experience
                                    </div>
                                    <% if (teacher.teaching_experience_years && teacher.teaching_experience_years !== teacher.total_experience_years) { %>
                                        <div class="text-xs text-gray-400">
                                            <%= teacher.teaching_experience_years %> years teaching
                                        </div>
                                    <% } %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <% if (teacher.last_login) { %>
                                        <%= new Date(teacher.last_login).toLocaleDateString() %>
                                    <% } else { %>
                                        Never
                                    <% } %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-16 bg-gray-200 rounded-full h-2 mr-3">
                                            <div class="h-2 rounded-full" style="width: <%= profileCompletion %>%; background-color: <%= progressBarColor %>;"></div>
                                        </div>
                                        <span class="text-sm font-medium <%= textColor %>">
                                            <%= profileCompletion %>%
                                        </span>
                                    </div>
                                    <div class="text-xs mt-1 <%= textColor %>">
                                        <%= completedFields %>/<%= totalFields %> fields
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button class="view-teacher-btn hover:bg-gray-100 p-1 rounded"
                                                data-teacher-id="<%= teacher.id %>"
                                                title="View Teacher Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn hover:bg-gray-100 p-1 rounded"
                                                data-action="send-message"
                                                data-teacher-id="<%= teacher.id %>"
                                                title="Send Message">
                                            <i class="fas fa-envelope"></i>
                                        </button>
                                        <button class="generate-cv-btn hover:bg-gray-100 p-1 rounded"
                                                data-teacher-id="<%= teacher.id %>"
                                                title="Generate CV PDF">
                                            <i class="fas fa-file-pdf"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <% }); %>
                    <% } else { %>
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <i class="fas fa-user-friends text-4xl text-gray-300 mb-4"></i>
                                <h3 class="text-lg font-medium text-gray-900">No teachers found</h3>
                                <p class="text-sm text-gray-500">Teacher data will appear here once teachers are added to the system.</p>
                            </td>
                        </tr>
                    <% } %>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Faculty Details Modal -->
<div id="teacherModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
  <div class="flex items-center justify-center min-h-screen p-1 sm:p-2">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-[98vw] max-h-[98vh] overflow-y-auto">
      <div class="bg-gray-100 p-4 rounded-t-lg border-b">
        <div class="flex justify-between items-center">
          <h3 id="teacherModalTitle" class="text-lg font-medium">Faculty Details</h3>
          <button id="closeTeacherModalBtn" class="hover:bg-gray-200 p-1 rounded">
            <i class="fas fa-times text-xl"></i>
          </button>
        </div>
      </div>

      <div id="teacherModalContent" class="p-3">
        <!-- Loading state -->
        <div id="modal-loading" class="flex items-center justify-center py-8">
          <i class="fas fa-spinner fa-spin text-2xl mr-2"></i>
          <span class="text-sm">Loading faculty profile...</span>
        </div>

        <!-- Faculty Profile Content (will be populated) -->
        <div id="enhanced-profile-content" class="hidden">
          <!-- Personal Information Section -->
          <div class="grid grid-cols-1 lg:grid-cols-4 gap-4 mb-6">
            <!-- Profile Image -->
            <div class="lg:col-span-1">
              <div class="text-center">
                <img id="modal-profile-image" class="w-24 h-24 rounded-full mx-auto mb-2 hidden" alt="Profile">
                <div id="modal-profile-image-placeholder" class="w-24 h-24 rounded-full bg-gray-300 flex items-center justify-center mx-auto mb-2 text-lg font-bold text-gray-600">
                  <!-- Initials will be populated -->
                </div>
                <div class="text-center">
                  <h3 id="modal-teacher-name" class="text-base font-semibold text-gray-900 mb-1">Loading...</h3>
                  <p id="modal-teacher-designation" class="text-xs text-gray-600">Loading...</p>
                  <p id="modal-teacher-department" class="text-xs text-gray-500">Loading...</p>
                </div>
              </div>
            </div>

            <!-- Basic Information -->
            <div class="lg:col-span-3">
              <div class="grid grid-cols-2 md:grid-cols-4 gap-3 text-xs">
                <div>
                  <label class="text-xs font-medium text-gray-600">Employee ID</label>
                  <p id="modal-teacher-employee-id" class="text-xs text-gray-900">Loading...</p>
                </div>
                <div>
                  <label class="text-xs font-medium text-gray-600">Email</label>
                  <p id="modal-teacher-email" class="text-xs text-gray-900">Loading...</p>
                </div>
                <div>
                  <label class="text-xs font-medium text-gray-600">Phone</label>
                  <p id="modal-teacher-phone" class="text-xs text-gray-900">Loading...</p>
                </div>
                <div>
                  <label class="text-xs font-medium text-gray-600">Username</label>
                  <p id="modal-username" class="text-xs text-gray-900">Loading...</p>
                </div>
                <div>
                  <label class="text-xs font-medium text-gray-600">Date of Birth</label>
                  <p id="modal-date-of-birth" class="text-xs text-gray-900">Loading...</p>
                </div>
                <div>
                  <label class="text-xs font-medium text-gray-600">Gender</label>
                  <p id="modal-gender" class="text-xs text-gray-900">Loading...</p>
                </div>
                <div>
                  <label class="text-xs font-medium text-gray-600">Joining Date</label>
                  <p id="modal-teacher-joining-date" class="text-xs text-gray-900">Loading...</p>
                </div>
                <div>
                  <label class="text-xs font-medium text-gray-600">Employment Type</label>
                  <p id="modal-teacher-employment-type" class="text-xs text-gray-900">Loading...</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Experience Summary -->
          <div class="grid grid-cols-3 gap-3 mb-4">
            <div class="bg-gray-50 p-2 rounded text-center">
              <div class="text-lg font-bold text-gray-900" id="modal-total-experience">0</div>
              <div class="text-xs text-gray-600">Total Exp</div>
            </div>
            <div class="bg-gray-50 p-2 rounded text-center">
              <div class="text-lg font-bold text-gray-900" id="modal-teaching-experience">0</div>
              <div class="text-xs text-gray-600">Teaching Exp</div>
            </div>
            <div class="bg-gray-50 p-2 rounded text-center">
              <div class="text-lg font-bold text-gray-900" id="modal-administrative-experience">0</div>
              <div class="text-xs text-gray-600">Admin Exp</div>
            </div>
          </div>

          <!-- Contact Information -->
          <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
              <h4 class="text-sm font-semibold text-gray-900">Contact Information</h4>
            </div>
            <div class="overflow-x-auto">
              <div class="flex space-x-3 pb-2 min-w-max">
                <div class="bg-white p-3 rounded border border-gray-200 min-w-[200px] flex-shrink-0">
                  <label class="text-xs font-medium text-gray-600">Emergency Contact</label>
                  <p id="modal-emergency-contact" class="text-xs text-gray-900">Loading...</p>
                </div>
                <div class="bg-white p-3 rounded border border-gray-200 min-w-[200px] flex-shrink-0">
                  <label class="text-xs font-medium text-gray-600">Alternate Phone</label>
                  <p id="modal-alternate-phone" class="text-xs text-gray-900">Loading...</p>
                </div>
                <div class="bg-white p-3 rounded border border-gray-200 min-w-[250px] flex-shrink-0">
                  <label class="text-xs font-medium text-gray-600">Address</label>
                  <p id="modal-address" class="text-xs text-gray-900">Loading...</p>
                </div>
                <div class="bg-white p-3 rounded border border-gray-200 min-w-[150px] flex-shrink-0">
                  <label class="text-xs font-medium text-gray-600">City</label>
                  <p id="modal-city" class="text-xs text-gray-900">Loading...</p>
                </div>
                <div class="bg-white p-3 rounded border border-gray-200 min-w-[150px] flex-shrink-0">
                  <label class="text-xs font-medium text-gray-600">State</label>
                  <p id="modal-state" class="text-xs text-gray-900">Loading...</p>
                </div>
                <div class="bg-white p-3 rounded border border-gray-200 min-w-[120px] flex-shrink-0">
                  <label class="text-xs font-medium text-gray-600">Pincode</label>
                  <p id="modal-pincode" class="text-xs text-gray-900">Loading...</p>
                </div>
                <div class="bg-white p-3 rounded border border-gray-200 min-w-[180px] flex-shrink-0">
                  <label class="text-xs font-medium text-gray-600">Office Location</label>
                  <p id="modal-office-location" class="text-xs text-gray-900">Loading...</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Professional Information -->
          <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
              <h4 class="text-sm font-semibold text-gray-900">Professional Information</h4>
            </div>
            <div class="overflow-x-auto">
              <div class="flex space-x-3 pb-2 min-w-max">
                <div class="bg-white p-3 rounded border border-gray-200 min-w-[180px] flex-shrink-0">
                  <label class="text-xs font-medium text-gray-600">Qualification</label>
                  <p id="modal-qualification" class="text-xs text-gray-900">Loading...</p>
                </div>
                <div class="bg-white p-3 rounded border border-gray-200 min-w-[180px] flex-shrink-0">
                  <label class="text-xs font-medium text-gray-600">Specialization</label>
                  <p id="modal-specialization" class="text-xs text-gray-900">Loading...</p>
                </div>
                <div class="bg-white p-3 rounded border border-gray-200 min-w-[150px] flex-shrink-0">
                  <label class="text-xs font-medium text-gray-600">Experience Years</label>
                  <p id="modal-experience-years" class="text-xs text-gray-900">Loading...</p>
                </div>
                <div class="bg-white p-3 rounded border border-gray-200 min-w-[160px] flex-shrink-0">
                  <label class="text-xs font-medium text-gray-600">Performance Rating</label>
                  <p id="modal-performance-rating" class="text-xs text-gray-900">Loading...</p>
                </div>
                <div class="bg-white p-3 rounded border border-gray-200 min-w-[150px] flex-shrink-0">
                  <label class="text-xs font-medium text-gray-600">Current Salary</label>
                  <p id="modal-current-salary" class="text-xs text-gray-900">Loading...</p>
                </div>
                <div class="bg-white p-3 rounded border border-gray-200 min-w-[180px] flex-shrink-0">
                  <label class="text-xs font-medium text-gray-600">Languages Known</label>
                  <p id="modal-languages-known" class="text-xs text-gray-900">Loading...</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Education Timeline -->
          <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
              <h4 class="text-sm font-semibold text-gray-900">Educational Background</h4>
              <div class="flex items-center">
                <div class="w-20 bg-gray-200 rounded-full h-1.5 mr-2">
                  <div id="education-progress-bar" class="h-1.5 rounded-full bg-blue-600" style="width: 0%;"></div>
                </div>
                <span id="education-completion-text" class="text-xs font-medium text-gray-600">0%</span>
              </div>
            </div>
            <div id="modal-education-timeline" class="overflow-x-auto">
              <div class="flex space-x-3 pb-2 min-w-max">
                <!-- Education timeline will be populated by JavaScript as horizontal cards -->
              </div>
            </div>
          </div>

          <!-- Experience Timeline -->
          <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
              <h4 class="text-sm font-semibold text-gray-900">Professional Experience</h4>
              <div class="flex items-center">
                <div class="w-20 bg-gray-200 rounded-full h-1.5 mr-2">
                  <div id="experience-progress-bar" class="h-1.5 rounded-full bg-green-600" style="width: 0%;"></div>
                </div>
                <span id="experience-completion-text" class="text-xs font-medium text-gray-600">0%</span>
              </div>
            </div>
            <div id="modal-experience-timeline" class="overflow-x-auto">
              <div class="flex space-x-3 pb-2 min-w-max">
                <!-- Experience timeline will be populated by JavaScript as horizontal cards -->
              </div>
            </div>
          </div>

          <!-- Skills -->
          <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
              <h4 class="text-sm font-semibold text-gray-900">Skills</h4>
              <div class="flex items-center">
                <div class="w-20 bg-gray-200 rounded-full h-1.5 mr-2">
                  <div id="skills-progress-bar" class="h-1.5 rounded-full bg-purple-600" style="width: 0%;"></div>
                </div>
                <span id="skills-completion-text" class="text-xs font-medium text-gray-600">0%</span>
              </div>
            </div>
            <div id="modal-skills-list" class="overflow-x-auto">
              <div class="flex space-x-3 pb-2 min-w-max">
                <!-- Skills will be populated by JavaScript as horizontal cards -->
              </div>
            </div>
          </div>

          <!-- Languages -->
          <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
              <h4 class="text-sm font-semibold text-gray-900">Languages</h4>
              <div class="flex items-center">
                <div class="w-20 bg-gray-200 rounded-full h-1.5 mr-2">
                  <div id="languages-progress-bar" class="h-1.5 rounded-full bg-indigo-600" style="width: 0%;"></div>
                </div>
                <span id="languages-completion-text" class="text-xs font-medium text-gray-600">0%</span>
              </div>
            </div>
            <div id="modal-languages-list" class="overflow-x-auto">
              <div class="flex space-x-3 pb-2 min-w-max">
                <!-- Languages will be populated by JavaScript as horizontal cards -->
              </div>
            </div>
          </div>

          <!-- Certifications -->
          <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
              <h4 class="text-sm font-semibold text-gray-900">Certifications</h4>
              <div class="flex items-center">
                <div class="w-20 bg-gray-200 rounded-full h-1.5 mr-2">
                  <div id="certifications-progress-bar" class="h-1.5 rounded-full bg-yellow-600" style="width: 0%;"></div>
                </div>
                <span id="certifications-completion-text" class="text-xs font-medium text-gray-600">0%</span>
              </div>
            </div>
            <div id="modal-certifications-list" class="overflow-x-auto">
              <div class="flex space-x-3 pb-2 min-w-max">
                <!-- Certifications will be populated by JavaScript as horizontal cards -->
              </div>
            </div>
          </div>

          <!-- Other Qualifications -->
          <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
              <h4 class="text-sm font-semibold text-gray-900">Other Qualifications</h4>
              <div class="flex items-center">
                <div class="w-20 bg-gray-200 rounded-full h-1.5 mr-2">
                  <div id="qualifications-progress-bar" class="h-1.5 rounded-full bg-orange-600" style="width: 0%;"></div>
                </div>
                <span id="qualifications-completion-text" class="text-xs font-medium text-gray-600">0%</span>
              </div>
            </div>
            <div id="modal-other-qualifications-list" class="overflow-x-auto">
              <div class="flex space-x-3 pb-2 min-w-max">
                <!-- Other qualifications will be populated by JavaScript as horizontal cards -->
              </div>
            </div>
          </div>

          <!-- Publications -->
          <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
              <h4 class="text-sm font-semibold text-gray-900">Publications</h4>
            </div>
            <div id="modal-publications-list" class="overflow-x-auto">
              <div class="flex space-x-3 pb-2 min-w-max">
                <!-- Publications will be populated by JavaScript as horizontal cards -->
              </div>
            </div>
          </div>

          <!-- Research Papers -->
          <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
              <h4 class="text-sm font-semibold text-gray-900">Research Papers</h4>
            </div>
            <div id="modal-research-papers-list" class="overflow-x-auto">
              <div class="flex space-x-3 pb-2 min-w-max">
                <!-- Research papers will be populated by JavaScript as horizontal cards -->
              </div>
            </div>
          </div>

          <!-- Conferences -->
          <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
              <h4 class="text-sm font-semibold text-gray-900">Conferences</h4>
            </div>
            <div id="modal-conferences-list" class="overflow-x-auto">
              <div class="flex space-x-3 pb-2 min-w-max">
                <!-- Conferences will be populated by JavaScript as horizontal cards -->
              </div>
            </div>
          </div>

          <!-- Awards -->
          <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
              <h4 class="text-sm font-semibold text-gray-900">Awards</h4>
              <div class="flex items-center">
                <div class="w-20 bg-gray-200 rounded-full h-1.5 mr-2">
                  <div id="awards-progress-bar" class="h-1.5 rounded-full bg-red-600" style="width: 0%;"></div>
                </div>
                <span id="awards-completion-text" class="text-xs font-medium text-gray-600">0%</span>
              </div>
            </div>
            <div id="modal-awards-list" class="overflow-x-auto">
              <div class="flex space-x-3 pb-2 min-w-max">
                <!-- Awards will be populated by JavaScript as horizontal cards -->
              </div>
            </div>
          </div>

          <!-- Training Programs -->
          <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
              <h4 class="text-sm font-semibold text-gray-900">Training Programs</h4>
              <div class="flex items-center">
                <div class="w-20 bg-gray-200 rounded-full h-1.5 mr-2">
                  <div id="training-progress-bar" class="h-1.5 rounded-full bg-teal-600" style="width: 0%;"></div>
                </div>
                <span id="training-completion-text" class="text-xs font-medium text-gray-600">0%</span>
              </div>
            </div>
            <div id="modal-training-list" class="overflow-x-auto">
              <div class="flex space-x-3 pb-2 min-w-max">
                <!-- Training programs will be populated by JavaScript as horizontal cards -->
              </div>
            </div>
          </div>

          <!-- Enhanced Achievements Section -->
          <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
              <h4 class="text-sm font-semibold text-gray-900">Enhanced Achievements</h4>
            </div>
            <div id="modal-achievements-content" class="overflow-x-auto">
              <div class="flex space-x-3 pb-2 min-w-max">
                <!-- Enhanced achievements will be populated by JavaScript as horizontal cards -->
              </div>
            </div>
          </div>

          <!-- Notes and Additional Information -->
          <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
              <h4 class="text-sm font-semibold text-gray-900">Additional Notes</h4>
            </div>
            <div id="modal-notes" class="text-xs text-gray-900 bg-gray-50 p-3 rounded">
              <!-- Notes will be populated by JavaScript -->
            </div>
          </div>

          <!-- Profile Completion -->
          <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
              <h4 class="text-sm font-semibold text-gray-900">Profile Completion</h4>
              <div class="flex items-center">
                <div class="w-24 bg-gray-200 rounded-full h-2 mr-2">
                  <div id="overall-progress-bar" class="h-2 rounded-full bg-gradient-to-r from-red-500 via-yellow-500 to-green-500" style="width: 0%;"></div>
                </div>
                <span id="overall-completion-text" class="text-xs font-bold text-gray-900">0%</span>
              </div>
            </div>
            <div id="modal-profile-completion" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2">
              <!-- Profile completion categories will be populated by JavaScript -->
              <div class="bg-gray-50 p-2 rounded">
                <div class="flex justify-between items-center mb-1">
                  <span class="text-xs font-medium text-gray-700">Basic Info</span>
                  <span id="basic-info-percentage" class="text-xs text-gray-600">0%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-1">
                  <div id="basic-info-progress" class="h-1 rounded-full bg-blue-600" style="width: 0%;"></div>
                </div>
              </div>
              <div class="bg-gray-50 p-2 rounded">
                <div class="flex justify-between items-center mb-1">
                  <span class="text-xs font-medium text-gray-700">Contact</span>
                  <span id="contact-percentage" class="text-xs text-gray-600">0%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-1">
                  <div id="contact-progress" class="h-1 rounded-full bg-green-600" style="width: 0%;"></div>
                </div>
              </div>
              <div class="bg-gray-50 p-2 rounded">
                <div class="flex justify-between items-center mb-1">
                  <span class="text-xs font-medium text-gray-700">Professional</span>
                  <span id="professional-percentage" class="text-xs text-gray-600">0%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-1">
                  <div id="professional-progress" class="h-1 rounded-full bg-purple-600" style="width: 0%;"></div>
                </div>
              </div>
              <div class="bg-gray-50 p-2 rounded">
                <div class="flex justify-between items-center mb-1">
                  <span class="text-xs font-medium text-gray-700">Education</span>
                  <span id="education-percentage" class="text-xs text-gray-600">0%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-1">
                  <div id="education-progress" class="h-1 rounded-full bg-indigo-600" style="width: 0%;"></div>
                </div>
              </div>
              <div class="bg-gray-50 p-2 rounded">
                <div class="flex justify-between items-center mb-1">
                  <span class="text-xs font-medium text-gray-700">Experience</span>
                  <span id="experience-percentage" class="text-xs text-gray-600">0%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-1">
                  <div id="experience-progress" class="h-1 rounded-full bg-yellow-600" style="width: 0%;"></div>
                </div>
              </div>
              <div class="bg-gray-50 p-2 rounded">
                <div class="flex justify-between items-center mb-1">
                  <span class="text-xs font-medium text-gray-700">Additional</span>
                  <span id="additional-percentage" class="text-xs text-gray-600">0%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-1">
                  <div id="additional-progress" class="h-1 rounded-full bg-red-600" style="width: 0%;"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div id="teacherModalFooter" class="px-4 sm:px-6 py-4 bg-gray-50 rounded-b-lg flex flex-col sm:flex-row justify-between items-center gap-2 sm:gap-0">
        <button id="printTeacherProfile" class="w-full sm:w-auto px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
          <i class="fas fa-file-pdf mr-2"></i>Download CV
        </button>
        <button id="closeTeacherModalBtn2" class="w-full sm:w-auto px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 text-sm">
          <i class="fas fa-times mr-2"></i>Close
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Enhanced Teacher Modal JavaScript -->
<script src="/js/enhanced-teacher-modal-combined.js"></script>

<!-- Simple JavaScript for table functionality -->
<script>
$(document).ready(function() {
    console.log('Teacher management page loaded');

    // Search functionality
    $('#search-teachers').on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        $('.teacher-row').each(function() {
            const name = $(this).data('name') || '';
            const email = $(this).data('email') || '';
            const visible = name.includes(searchTerm) || email.includes(searchTerm);
            $(this).toggle(visible);
        });
    });

    // Filter functionality
    $('#filter-completion').on('change', function() {
        const filterValue = $(this).val();
        $('.teacher-row').each(function() {
            const completion = $(this).data('profile-completion');
            const visible = !filterValue || completion === filterValue;
            $(this).toggle(visible);
        });
    });

    // View teacher button - connect to enhanced modal
    $(document).on('click', '.view-teacher-btn', function() {
        const teacherId = $(this).data('teacher-id');
        console.log('View teacher button clicked for ID:', teacherId);

        // Call the enhanced modal function
        if (window.openEnhancedTeacherModal) {
            window.openEnhancedTeacherModal(teacherId);
        } else {
            console.error('Enhanced modal function not available');
            alert('Modal functionality not loaded. Please refresh the page.');
        }
    });

    // Close modal buttons
    $(document).on('click', '#closeTeacherModalBtn, #closeTeacherModalBtn2', function() {
        if (window.closeEnhancedTeacherModal) {
            window.closeEnhancedTeacherModal();
        }
    });

    // PDF generation button
    $(document).on('click', '#printTeacherProfile', function() {
        console.log('PDF generation requested');
        if (window.currentTeacherData) {
            // Call PDF generation function if available
            if (window.generateTeacherPDF) {
                window.generateTeacherPDF(window.currentTeacherData);
            } else {
                console.log('PDF generation function not available');
                alert('PDF generation functionality will be implemented soon.');
            }
        } else {
            console.error('No teacher data available for PDF generation');
            alert('No teacher data available. Please try again.');
        }
    });

    // Generate CV button in table
    $(document).on('click', '.generate-cv-btn', function() {
        const teacherId = $(this).data('teacher-id');
        console.log('Generate CV for teacher ID:', teacherId);
        alert(`CV generation for teacher ID: ${teacherId} - To be implemented`);
    });

    // Send message button in table
    $(document).on('click', '.action-btn[data-action="send-message"]', function() {
        const teacherId = $(this).data('teacher-id');
        console.log('Send message to teacher ID:', teacherId);
        alert(`Message functionality for teacher ID: ${teacherId} - To be implemented`);
    });
});
</script>
