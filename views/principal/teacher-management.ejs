<!-- Page-specific styles for teacher management -->
<style>
    .teacher-row {
        transition: all 0.2s ease;
    }
    .teacher-row:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    /* CV Generation Button Styling */
    .generate-cv-btn {
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
    }

    .generate-cv-btn:hover {
        transform: scale(1.1);
        color: #059669 !important;
    }

    .generate-cv-btn:active {
        transform: scale(0.95);
    }

    /* Button loading state */
    .generate-cv-btn.loading {
        pointer-events: none;
        opacity: 0.7;
    }

    .generate-cv-btn.loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 12px;
        height: 12px;
        margin: -6px 0 0 -6px;
        border: 2px solid transparent;
        border-top: 2px solid currentColor;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>

<!-- Faculty Details Overview -->
<div class="mb-8">
    <!-- Page Header -->
    <div class="mb-6">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Faculty Details</h1>
        <p class="text-gray-600">Comprehensive faculty profile management and completion tracking</p>
    </div>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-gray-100">
                    <i class="fas fa-users text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium">Total Teachers</p>
                    <p class="text-2xl font-bold"><%= teachers.length %></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-gray-100">
                    <i class="fas fa-user-check text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium">Complete Profiles</p>
                    <p class="text-2xl font-bold">
                        <%= teachers.filter(t => {
                            let fields = 0, completed = 0;
                            fields += 8; // Basic fields
                            if (t.name) completed++; if (t.email) completed++; if (t.full_name) completed++;
                            if (t.date_of_birth) completed++; if (t.bio) completed++; if (t.profile_image) completed++;
                            if (t.subjects) completed++; if (t.last_login) completed++;
                            return Math.round((completed / fields) * 100) >= 80;
                        }).length %>
                    </p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-gray-100">
                    <i class="fas fa-user-edit text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium">Incomplete Profiles</p>
                    <p class="text-2xl font-bold">
                        <%= teachers.filter(t => {
                            let fields = 0, completed = 0;
                            fields += 8; // Basic fields
                            if (t.name) completed++; if (t.email) completed++; if (t.full_name) completed++;
                            if (t.date_of_birth) completed++; if (t.bio) completed++; if (t.profile_image) completed++;
                            if (t.subjects) completed++; if (t.last_login) completed++;
                            return Math.round((completed / fields) * 100) < 80;
                        }).length %>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Faculty Details Table -->
<div class="bg-white rounded-lg shadow-md">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-xl font-bold text-gray-900">Faculty Profile Dashboard</h2>
                <p class="text-sm text-gray-600 mt-1">Comprehensive faculty profile management and completion tracking</p>
            </div>
            <div class="flex items-center space-x-4">
                <!-- Export Button -->
                <button class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    <i class="fas fa-download mr-2"></i>
                    Export Report
                </button>
                <!-- Test PDF Button -->
                <button id="testSimplePDF" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    <i class="fas fa-file-pdf mr-2"></i>
                    Test PDF
                </button>
                <!-- Refresh Button -->
                <button class="action-btn bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium" data-action="refresh">
                    <i class="fas fa-sync-alt mr-2"></i>
                    Refresh
                </button>
            </div>
        </div>
    </div>

    <div class="p-6">
        <!-- Search and Filter -->
        <div class="mb-6 flex flex-col sm:flex-row gap-4">
            <div class="flex-1">
                <input type="text" id="search-teachers" placeholder="Search teachers by name or email..."
                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-transparent text-sm">
            </div>
            <div class="flex gap-2">
                <select id="filter-profile-completion" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-transparent text-sm">
                    <option value="">All Profiles</option>
                    <option value="complete">Complete (80%+)</option>
                    <option value="good">Good (60-79%)</option>
                    <option value="incomplete">Incomplete (40-59%)</option>
                    <option value="poor">Poor (<40%)</option>
                </select>
            </div>
        </div>

        <% if (teachers && teachers.length > 0) { %>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 table-principal">
                    <thead>
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Teacher
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Department
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Experience
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Last Login
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Profile Completion
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="teachers-table-body">
                        <% teachers.forEach(teacher => { %>
                            <%
                                // Calculate profile completion based on available fields
                                // Note: This is a basic calculation. The modal will show more accurate completion using enhanced API data
                                let profileFields = 0;
                                let completedFields = 0;

                                // Helper function to check if field has meaningful value
                                function hasValue(value) {
                                    return value !== null &&
                                           value !== undefined &&
                                           value !== 'null' &&
                                           value !== 'undefined' &&
                                           value.toString().trim() !== '' &&
                                           value.toString().trim() !== '0' &&
                                           value.toString().trim() !== 'Not provided';
                                }

                                // Basic user fields from users table (8 fields)
                                const basicFields = ['name', 'email', 'full_name', 'username', 'date_of_birth', 'bio', 'profile_image', 'subjects'];
                                basicFields.forEach(field => {
                                    profileFields++;
                                    if (teacher[field] && hasValue(teacher[field])) {
                                        completedFields++;
                                    }
                                });

                                // Additional fields that might be available in the teacher object (12 fields)
                                const additionalFields = ['phone', 'designation', 'department', 'joining_date', 'employment_type', 'employee_id', 'address', 'emergency_contact', 'qualification', 'specialization', 'experience_years', 'office_location'];
                                additionalFields.forEach(field => {
                                    profileFields++;
                                    if (teacher[field] && hasValue(teacher[field])) {
                                        completedFields++;
                                    }
                                });

                                // Estimate completion based on available data (will be updated when modal opens)
                                let profileCompletion = profileFields > 0 ? Math.round((completedFields / profileFields) * 100) : 0;

                                // If we have very few completed fields, show a more realistic estimate
                                if (completedFields <= 3) {
                                    profileCompletion = Math.max(15, profileCompletion); // Show at least 15% for active teachers
                                }

                                const profileLevel = profileCompletion >= 80 ? 'complete' : profileCompletion >= 60 ? 'good' : profileCompletion >= 40 ? 'incomplete' : 'poor';
                                const progressBarColor = profileCompletion >= 80 ? '#10b981' : profileCompletion >= 60 ? '#3b82f6' : profileCompletion >= 40 ? '#eab308' : '#ef4444';
                            %>
                            <tr class="hover:bg-gray-50 teacher-row"
                                data-teacher-id="<%= teacher.id %>"
                                data-name="<%= teacher.name.toLowerCase() %>"
                                data-email="<%= teacher.email.toLowerCase() %>"
                                data-profile-completion="<%= profileLevel %>">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center">
                                                <span class="text-sm font-medium">
                                                    <%= teacher.name.split(' ').map(n => n[0]).join('').toUpperCase() %>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">
                                                <%= teacher.name %>
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                <%= teacher.email %>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <span class="font-medium"><%= teacher.department || 'Academic' %></span>
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        <%= teacher.designation || 'Teacher' %>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <span class="font-medium"><%= teacher.experience_years || teacher.total_experience_years || '0' %></span> years
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        Teaching experience
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <% if (teacher.last_login) { %>
                                        <%= new Date(teacher.last_login).toLocaleDateString() %>
                                    <% } else { %>
                                        Never
                                    <% } %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-16 bg-gray-200 rounded-full h-2 mr-3">
                                            <div class="h-2 rounded-full bg-gray-500" style="width: <%= profileCompletion %>%"></div>
                                        </div>
                                        <span class="text-sm font-medium">
                                            <%= profileCompletion %>%
                                        </span>
                                    </div>
                                    <div class="text-xs mt-1">
                                        <%= completedFields %>/<%= profileFields %> fields
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button id="viewTeacherBtn-<%= teacher.id %>"
                                                class="view-teacher-btn hover:bg-gray-100 p-1 rounded"
                                                data-teacher-id="<%= teacher.id %>"
                                                title="View Teacher Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn hover:bg-gray-100 p-1 rounded"
                                                data-action="send-message"
                                                data-teacher-id="<%= teacher.id %>"
                                                title="Send Message">
                                            <i class="fas fa-envelope"></i>
                                        </button>
                                        <button id="generateCVBtn-<%= teacher.id %>"
                                                class="generate-cv-btn hover:bg-gray-100 p-1 rounded"
                                                data-action="generate-cv"
                                                data-teacher-id="<%= teacher.id %>"
                                                data-teacher-name="<%= teacher.name %>"
                                                data-teacher-email="<%= teacher.email %>"
                                                title="Generate CV PDF">
                                            <i class="fas fa-file-pdf"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        <% } else { %>
            <div class="text-center py-12">
                <i class="fas fa-user-friends text-4xl text-gray-300 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900">No teachers found</h3>
                <p class="text-sm text-gray-500">Teacher data will appear here once teachers are added to the system.</p>
            </div>
        <% } %>
    </div>
</div>

<!-- Faculty Details Modal -->
<div id="teacherModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
  <div class="flex items-center justify-center min-h-screen p-2 sm:p-4">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[95vh] overflow-y-auto">
      <div class="bg-gray-100 p-4 rounded-t-lg border-b">
        <div class="flex justify-between items-center">
          <h3 id="teacherModalTitle" class="text-lg font-medium">Faculty Details</h3>
          <button id="closeTeacherModalBtn" class="hover:bg-gray-200 p-1 rounded">
            <i class="fas fa-times text-xl"></i>
          </button>
        </div>
      </div>

      <div id="teacherModalContent" class="p-4 sm:p-6">
        <!-- Loading state -->
        <div id="modal-loading" class="flex items-center justify-center py-12">
          <i class="fas fa-spinner fa-spin text-3xl mr-3"></i>
          <span class="text-sm">Loading faculty profile...</span>
        </div>

        <!-- Faculty Profile Content (will be populated) -->
        <div id="enhanced-profile-content" class="hidden">
          <!-- 4-Column Grid Layout -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <!-- Personal Information -->
            <div>
              <h4 class="text-base font-semibold mb-3">Personal Information</h4>
              <div class="space-y-2 text-sm">
                <div><strong>Name:</strong> <span id="modal-teacher-name" class="text-gray-600">-</span></div>
                <div><strong>Employee ID:</strong> <span id="modal-teacher-employee-id" class="text-gray-600">-</span></div>
                <div><strong>Email:</strong> <span id="modal-teacher-email" class="text-gray-600">-</span></div>
                <div><strong>Phone:</strong> <span id="modal-teacher-phone" class="text-gray-600">-</span></div>
                <div><strong>DOB:</strong> <span id="modal-date-of-birth" class="text-gray-600">-</span></div>
                <div><strong>Gender:</strong> <span id="modal-gender" class="text-gray-600">-</span></div>
                <div><strong>Username:</strong> <span id="modal-username" class="text-gray-600">-</span></div>
              </div>
            </div>

            <!-- Professional Information -->
            <div>
              <h4 class="text-base font-semibold mb-3">Professional Information</h4>
              <div class="space-y-2 text-sm">
                <div><strong>Designation:</strong> <span id="modal-teacher-designation" class="text-gray-600">-</span></div>
                <div><strong>Department:</strong> <span id="modal-teacher-department" class="text-gray-600">-</span></div>
                <div><strong>Joining Date:</strong> <span id="modal-teacher-joining-date" class="text-gray-600">-</span></div>
                <div><strong>Employment Type:</strong> <span id="modal-teacher-employment-type" class="text-gray-600">-</span></div>
                <div><strong>Qualification:</strong> <span id="modal-qualification" class="text-gray-600">-</span></div>
                <div><strong>Specialization:</strong> <span id="modal-specialization" class="text-gray-600">-</span></div>
                <div><strong>Experience:</strong> <span id="modal-experience-years" class="text-gray-600">-</span></div>
              </div>
            </div>

            <!-- Contact & Address -->
            <div>
              <h4 class="text-base font-semibold mb-3">Contact & Address</h4>
              <div class="space-y-2 text-sm">
                <div><strong>Emergency Contact:</strong> <span id="modal-emergency-contact" class="text-gray-600">-</span></div>
                <div><strong>Address:</strong> <span id="modal-address" class="text-gray-600">-</span></div>
                <div><strong>Office Location:</strong> <span id="modal-office-location" class="text-gray-600">-</span></div>
                <div><strong>Languages:</strong> <span id="modal-languages-known" class="text-gray-600">-</span></div>
              </div>
            </div>

            <!-- Profile Completion -->
            <div>
              <h4 class="text-base font-semibold mb-3">Profile Completion</h4>
              <div class="space-y-2 text-sm">
                <div><strong>Overall:</strong> <span id="summary-profile-completion-percentage" class="text-gray-600">0%</span></div>
                <div><strong>Basic Info:</strong> <span id="basic-info-completion" class="text-gray-600">0/8</span></div>
                <div><strong>Professional:</strong> <span id="professional-completion" class="text-gray-600">0/12</span></div>
                <div><strong>Total Fields:</strong> <span id="total-fields-count" class="text-gray-600">35</span></div>
                <div><strong>Completed:</strong> <span id="completed-fields-count" class="text-gray-600">0</span></div>
              </div>
            </div>
          </div>

          <!-- Additional Information Sections -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Education Background -->
            <div class="mb-4">
              <h4 class="text-base font-semibold mb-3">Education Background</h4>
              <div id="modal-education-timeline" class="space-y-2 text-sm">
                <!-- Education timeline will be populated here -->
                <div class="text-gray-600">No education data available</div>
              </div>
            </div>

            <!-- Skills & Expertise -->
            <div class="mb-4">
              <h4 class="text-base font-semibold mb-3">Skills & Expertise</h4>
              <div id="modal-skills-list" class="space-y-2 text-sm">
                <!-- Skills will be populated here -->
                <div class="text-gray-600">No skills data available</div>
              </div>
            </div>

            <!-- Professional Experience -->
            <div class="mb-4">
              <h4 class="text-base font-semibold mb-3">Professional Experience</h4>
              <div id="modal-experience-timeline" class="space-y-2 text-sm">
                <!-- Experience timeline will be populated here -->
                <div class="text-gray-600">No experience data available</div>
              </div>
            </div>

            <!-- Certifications -->
            <div class="mb-4">
              <h4 class="text-base font-semibold mb-3">Certifications</h4>
              <div id="modal-certifications-list" class="space-y-2 text-sm">
                <!-- Certifications will be populated here -->
                <div class="text-gray-600">No certifications available</div>
              </div>
            </div>

            <!-- Achievements & Recognition -->
            <div class="mb-4">
              <h4 class="text-base font-semibold mb-3">Achievements & Recognition</h4>
              <div id="modal-achievements-content" class="space-y-2 text-sm">
                <!-- Achievements will be populated here -->
                <div class="text-gray-600">No achievements recorded</div>
              </div>
            </div>

            <!-- Additional Notes -->
            <div class="mb-4">
              <h4 class="text-base font-semibold mb-3">Additional Notes</h4>
              <div id="modal-notes" class="space-y-2 text-sm">
                <!-- Notes will be populated here -->
                <div class="text-gray-600">No additional notes</div>
              </div>
            </div>
          </div>

        </div>
      </div>
      <div id="teacherModalFooter" class="px-4 sm:px-6 py-4 bg-gray-50 rounded-b-lg flex flex-col sm:flex-row justify-between items-center gap-2 sm:gap-0">
        <button id="printTeacherProfile" class="w-full sm:w-auto px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 text-sm">
          <i class="fas fa-file-pdf mr-2"></i>Download CV
        </button>
        <button id="closeTeacherModalBtn2" class="w-full sm:w-auto px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 text-sm">
          <i class="fas fa-times mr-2"></i>Close
        </button>
      </div>
    </div>
  </div>
</div>



<!-- Custom CSS for compact layout -->
<style>
.line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Compact modal spacing */
.enhanced-achievements-section .achievement-category {
    margin-bottom: 1rem;
}

/* Responsive grid improvements */
@media (min-width: 1024px) {
    .achievement-category .grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
}

/* Smooth transitions */
.achievement-category > div {
    transition: all 0.2s ease-in-out;
}

/* Compact timeline spacing */
.flex.space-x-4 {
    gap: 1rem;
}

.flex.space-x-6 {
    gap: 1.5rem;
}

/* Modal content optimization */
#enhanced-profile-content .space-y-4 > * + * {
    margin-top: 1rem;
}

#enhanced-profile-content .space-y-6 > * + * {
    margin-top: 1.5rem;
}

/* Ensure full width utilization */
.modal-content-container {
    max-width: none;
    width: 95vw;
}

/* Modal footer styling */
#teacherModalFooter {
    margin-top: 1.5rem;
    background-color: #f9fafb;
    border-top: 1px solid #e5e7eb;
}

/* Make sure PDF button is visible and styled */
#printTeacherProfile {
    min-width: 150px;
    font-weight: 500;
    display: flex !important;
    align-items: center;
    justify-content: center;
    background-color: #2563eb !important;
    color: white !important;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 20;
}

#printTeacherProfile:hover {
    background-color: #1d4ed8 !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Ensure modal footer is always visible */
#teacherModalFooter {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .modal-content-container {
        width: 98vw;
        margin: 0.5rem;
    }

    .achievement-category .grid {
        grid-template-columns: 1fr;
    }
}
</style>

<!-- All JavaScript functionality has been moved to external files for better organization -->

<!-- CV Generator Script -->
<script src="/js/cv-generator.js"></script>

<!-- DIRECT PDF BUTTON HANDLERS - SIMPLE APPROACH -->
<script>
console.log('🚀 Loading direct PDF handlers...');

// Direct button click handlers - no data attributes needed
$(document).ready(function() {
    console.log('✅ Document ready - setting up direct handlers');

    // Test PDF button - direct selector
    $('.pdf-action-btn, [data-action="test-pdf"]').on('click', function(e) {
        e.preventDefault();
        console.log('� TEST PDF BUTTON CLICKED!');

        const button = $(this);
        const originalHtml = button.html();
        button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Generating...');

        setTimeout(() => {
            try {
                const htmlContent = `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Test PDF</title>
                    <style>
                        @page { size: A4; margin: 20mm; }
                        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
                        h1 { color: #2563eb; }
                        .content { line-height: 1.6; }
                        @media print { .no-print { display: none; } }
                    </style>
                </head>
                <body>
                    <div class='no-print' style='background: #f0f0f0; padding: 15px; margin-bottom: 20px; border-radius: 5px;'>
                        <h3>✅ PDF Test Document</h3>
                        <p>Use <strong>Ctrl+P</strong> or <strong>Cmd+P</strong> to print this as PDF</p>
                        <button onclick='window.print()' style='background: #2563eb; color: white; border: none; padding: 8px 16px; border-radius: 4px; margin-right: 10px; cursor: pointer;'>Print as PDF</button>
                        <button onclick='window.close()' style='background: #666; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;'>Close</button>
                    </div>
                    <div class='content'>
                        <h1>✅ PDF Generation Test SUCCESS</h1>
                        <p><strong>Generated at:</strong> ${new Date().toLocaleString()}</p>
                        <p>This confirms PDF functionality is working perfectly!</p>
                        <p><strong>System:</strong> Teacher Management System</p>
                        <p><strong>Test Status:</strong> ✅ SUCCESS</p>
                        <hr>
                        <p><em>This document was generated using the browser's native print-to-PDF functionality.</em></p>
                    </div>
                </body>
                </html>
                `;

                const printWindow = window.open('', '_blank');
                printWindow.document.write(htmlContent);
                printWindow.document.close();

                button.html('<i class="fas fa-check mr-2"></i>PDF Opened!');
                setTimeout(() => {
                    button.html(originalHtml);
                    button.prop('disabled', false);
                }, 2000);

                console.log('✅ Test PDF generated successfully!');

            } catch(error) {
                console.error('❌ Test PDF error:', error);
                button.html('<i class="fas fa-times mr-2"></i>Error');
                setTimeout(() => {
                    button.html(originalHtml);
                    button.prop('disabled', false);
                }, 2000);
            }
        }, 500);
    });

    // CV Generation buttons - multiple selectors
    $('.generate-cv-btn, .cv-generate-btn, [data-action="generate-cv"]').on('click', function(e) {
        e.preventDefault();
        console.log('🔥 CV GENERATION BUTTON CLICKED!');

        const button = $(this);
        const originalHtml = button.html();
        button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Generating CV...');

        setTimeout(() => {
            try {
                // Get teacher data
                let teacher;
                const teacherId = button.data('teacher-id');

                if (window.currentTeacherData) {
                    teacher = window.currentTeacherData;
                    console.log('✅ Using cached teacher data');
                } else if (teacherId) {
                    // Get data from table row
                    const teacherRow = button.closest('tr');
                    const teacherName = teacherRow.find('.text-sm.font-medium').first().text().trim();
                    const teacherEmail = teacherRow.find('.text-sm.text-gray-500').first().text().trim();

                    teacher = {
                        id: teacherId,
                        name: teacherName || 'Teacher Name',
                        email: teacherEmail || '<EMAIL>',
                        designation: 'Teacher',
                        department: 'Academic Department',
                        employee_id: `EMP${String(teacherId).padStart(4, '0')}`,
                        joining_date: new Date().toISOString().split('T')[0],
                        employment_type: 'Permanent',
                        account_status: 'Active'
                    };
                    console.log('✅ Created teacher object from table:', teacher);
                } else {
                    // Default teacher data
                    teacher = {
                        name: 'Sample Teacher',
                        designation: 'Teacher',
                        department: 'Academic Department',
                        employee_id: 'EMP0001',
                        email: '<EMAIL>',
                        joining_date: new Date().toISOString().split('T')[0],
                        employment_type: 'Permanent',
                        account_status: 'Active'
                    };
                    console.log('✅ Using default teacher data');
                }

                // Generate CV HTML
                const cvHTML = `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>${teacher.name || 'Teacher'} - Curriculum Vitae</title>
                    <style>
                        @page { size: A4; margin: 15mm; }
                        body { font-family: 'Times New Roman', serif; margin: 0; padding: 0; line-height: 1.4; color: #333; }
                        .header { text-align: center; border-bottom: 3px solid #2563eb; padding-bottom: 15px; margin-bottom: 20px; }
                        .header h1 { color: #2563eb; margin: 0; font-size: 24px; }
                        .header h2 { color: #666; margin: 5px 0; font-size: 16px; font-weight: normal; }
                        .section { margin-bottom: 20px; }
                        .section-title { background: #2563eb; color: white; padding: 8px 15px; margin-bottom: 10px; font-weight: bold; }
                        .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px; }
                        .info-item { margin-bottom: 8px; }
                        .info-label { font-weight: bold; color: #2563eb; }
                        .footer { text-align: center; margin-top: 30px; padding-top: 15px; border-top: 1px solid #ccc; font-size: 12px; color: #666; }
                        @media print { .no-print { display: none; } }
                    </style>
                </head>
                <body>
                    <div class='no-print' style='background: #e3f2fd; padding: 15px; margin-bottom: 20px; border-radius: 5px; border: 1px solid #2196f3;'>
                        <h3 style='margin: 0 0 10px 0; color: #1976d2;'>✅ Teacher CV Document</h3>
                        <p style='margin: 0 0 10px 0;'>Use <strong>Ctrl+P</strong> (Windows) or <strong>Cmd+P</strong> (Mac) to save as PDF</p>
                        <button onclick='window.print()' style='background: #2196f3; color: white; border: none; padding: 8px 16px; border-radius: 4px; margin-right: 10px; cursor: pointer;'>Print as PDF</button>
                        <button onclick='window.close()' style='background: #666; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;'>Close</button>
                    </div>
                    <div class='header'>
                        <h1>${teacher.name || 'Teacher Name'}</h1>
                        <h2>${teacher.designation || 'Teacher'}</h2>
                        <p>${teacher.department || 'Academic Department'}</p>
                    </div>
                    <div class='section'>
                        <div class='section-title'>PERSONAL INFORMATION</div>
                        <div class='info-grid'>
                            <div>
                                <div class='info-item'><span class='info-label'>Employee ID:</span> ${teacher.employee_id || 'N/A'}</div>
                                <div class='info-item'><span class='info-label'>Email:</span> ${teacher.email || 'N/A'}</div>
                                <div class='info-item'><span class='info-label'>Phone:</span> ${teacher.phone || 'N/A'}</div>
                                <div class='info-item'><span class='info-label'>Date of Birth:</span> ${teacher.date_of_birth || 'N/A'}</div>
                            </div>
                            <div>
                                <div class='info-item'><span class='info-label'>Gender:</span> ${teacher.gender || 'N/A'}</div>
                                <div class='info-item'><span class='info-label'>Joining Date:</span> ${teacher.joining_date || 'N/A'}</div>
                                <div class='info-item'><span class='info-label'>Employment Type:</span> ${teacher.employment_type || 'N/A'}</div>
                                <div class='info-item'><span class='info-label'>Account Status:</span> ${teacher.account_status || 'N/A'}</div>
                            </div>
                        </div>
                    </div>
                    <div class='section'>
                        <div class='section-title'>PROFESSIONAL EXPERIENCE</div>
                        <div class='info-item'><span class='info-label'>Total Experience:</span> ${teacher.total_experience_years || 0} years</div>
                        <div class='info-item'><span class='info-label'>Teaching Experience:</span> ${teacher.teaching_experience_years || 0} years</div>
                        <div class='info-item'><span class='info-label'>Subjects Taught:</span> ${teacher.subjects_taught || 'N/A'}</div>
                    </div>
                    <div class='section'>
                        <div class='section-title'>ADMINISTRATIVE DETAILS</div>
                        <div class='info-grid'>
                            <div>
                                <div class='info-item'><span class='info-label'>Office Location:</span> ${teacher.office_location || 'N/A'}</div>
                                <div class='info-item'><span class='info-label'>Performance Rating:</span> ${teacher.performance_rating || 'N/A'}</div>
                            </div>
                            <div>
                                <div class='info-item'><span class='info-label'>Confirmation Date:</span> ${teacher.confirmation_date || 'N/A'}</div>
                                <div class='info-item'><span class='info-label'>Last Promotion:</span> ${teacher.last_promotion || 'N/A'}</div>
                            </div>
                        </div>
                    </div>
                    <div class='footer'>
                        <p><strong>Generated on:</strong> ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
                        <p>Teacher Management System - Curriculum Vitae</p>
                    </div>
                </body>
                </html>
                `;

                const cvWindow = window.open('', '_blank');
                cvWindow.document.write(cvHTML);
                cvWindow.document.close();

                button.html('<i class="fas fa-check mr-2"></i>CV Generated!');
                setTimeout(() => {
                    button.html(originalHtml);
                    button.prop('disabled', false);
                }, 2000);

                console.log('✅ CV generated successfully!');

            } catch(error) {
                console.error('❌ CV generation error:', error);
                button.html('<i class="fas fa-times mr-2"></i>Error');
                setTimeout(() => {
                    button.html(originalHtml);
                    button.prop('disabled', false);
                }, 2000);
            }
        }, 500);
    });

    // Refresh button handler
    $('.action-btn[data-action="refresh"], [data-action="refresh"]').on('click', function(e) {
        e.preventDefault();
        console.log('🔥 REFRESH BUTTON CLICKED!');

        const button = $(this);
        const originalHtml = button.html();
        button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Refreshing...');

        setTimeout(() => {
            location.reload();
        }, 500);
    });

    // Debug: List all buttons found
    setTimeout(() => {
        console.log('🔍 BUTTON DETECTION REPORT:');
        console.log('PDF Action buttons:', $('.pdf-action-btn').length);
        console.log('Generate CV buttons:', $('.generate-cv-btn').length);
        console.log('CV Generate buttons:', $('.cv-generate-btn').length);
        console.log('Data action test-pdf:', $('[data-action="test-pdf"]').length);
        console.log('Data action generate-cv:', $('[data-action="generate-cv"]').length);
        console.log('Data action refresh:', $('[data-action="refresh"]').length);

        // Test manual trigger
        window.triggerTestPDF = function() {
            console.log('🧪 Manual trigger test PDF');
            $('.pdf-action-btn, [data-action="test-pdf"]').first().trigger('click');
        };

        window.triggerCVGeneration = function() {
            console.log('🧪 Manual trigger CV generation');
            $('.generate-cv-btn, .cv-generate-btn, [data-action="generate-cv"]').first().trigger('click');
        };

        console.log('✅ Manual trigger functions available: triggerTestPDF(), triggerCVGeneration()');
    }, 1000);
});
</script>

<!-- Simple PDF Library - Local fallback for reliable PDF generation -->
<script src="/js/jspdf-simple.js"></script>
<script>
// Ensure jsPDF is available globally
if (typeof window.jsPDF === 'function') {
    window.jsPDF = window.jsPDF;
    // Also make it available without window prefix
    if (typeof jsPDF === 'undefined') {
        var jsPDF = window.jsPDF;
    }
    console.log('✅ jsPDF made globally available');
} else {
    console.error('❌ window.jsPDF not found after loading library');
}
</script>
<script src="/js/enhanced-teacher-modal-combined.js?v=1748751732798"></script>
<script src="/js/teacher-profile-pdf-generator.js"></script>

<!-- Simple test PDF function - defined globally -->
<script>
// Check jsPDF library availability
function checkJsPDFAvailability() {
    console.log('🔍 Checking jsPDF availability...');
    console.log('window.jspdf:', typeof window.jspdf);
    console.log('window.jsPDF:', typeof window.jsPDF);
    console.log('window.jspdf?.jsPDF:', typeof window.jspdf?.jsPDF);

    if (window.jspdf && window.jspdf.jsPDF) {
        console.log('✅ jsPDF available via window.jspdf.jsPDF');
        return window.jspdf.jsPDF;
    } else if (window.jsPDF) {
        console.log('✅ jsPDF available via window.jsPDF');
        return window.jsPDF;
    } else {
        console.log('❌ jsPDF not available');
        return null;
    }
}

// Simple test PDF function - GLOBAL SCOPE
function testPDFClick() {
    console.log('🚨 INLINE ONCLICK TRIGGERED!');
    alert('Inline onclick works! Now testing PDF...');

    try {
        // Check if jsPDF is available
        console.log('Checking jsPDF...');
        console.log('window.jspdf:', typeof window.jspdf);
        console.log('window.jsPDF:', typeof window.jsPDF);

        let jsPDFClass;
        if (window.jspdf && window.jspdf.jsPDF) {
            jsPDFClass = window.jspdf.jsPDF;
            console.log('Using window.jspdf.jsPDF');
        } else if (window.jsPDF) {
            jsPDFClass = window.jsPDF;
            console.log('Using window.jsPDF');
        } else {
            alert('jsPDF library not found!');
            return;
        }

        // Create simple PDF
        console.log('Creating PDF...');
        const doc = new jsPDFClass();
        doc.text('Test PDF from inline function', 20, 20);
        doc.text('Date: ' + new Date().toLocaleString(), 20, 40);
        doc.save('inline-test.pdf');

        console.log('✅ PDF created successfully!');
        alert('PDF generated successfully!');
    } catch (error) {
        console.error('❌ Error:', error);
        alert('Error: ' + error.message);
    }
}

$(document).ready(function() {
    console.log('Teacher management page loaded');
    console.log('Enhanced modal functions available:', typeof window.openEnhancedTeacherModal);

    // Check jsPDF library on page load
    setTimeout(function() {
        console.log('🔍 Checking jsPDF after page load...');
        console.log('window.jsPDF:', typeof window.jsPDF);
        console.log('window.SimplePDF:', typeof window.SimplePDF);

        // Test if we can create an instance
        try {
            const testDoc = new jsPDF();
            console.log('✅ jsPDF constructor works:', testDoc);
        } catch(e) {
            console.error('❌ jsPDF constructor failed:', e);
        }

        checkJsPDFAvailability();
    }, 1000);

    // Debug button existence
    console.log('🔍 Checking buttons on page load:');
    console.log('Test PDF button exists:', $('#testPDFBtn').length);
    console.log('Test PDF button HTML:', $('#testPDFBtn').html());

    // Add immediate click handler for testing
    $('#testPDFBtn').click(function() {
        console.log('🚨 JQUERY CLICK HANDLER TRIGGERED!');

        try {
            if (window.jspdf && window.jspdf.jsPDF) {
                const doc = new window.jspdf.jsPDF();
                doc.text('jQuery Test PDF', 20, 20);
                doc.text('Generated at: ' + new Date().toLocaleString(), 20, 40);
                doc.save('jquery-test.pdf');
                alert('jQuery PDF generated!');
            } else if (window.jsPDF) {
                const doc = new window.jsPDF();
                doc.text('jQuery Test PDF', 20, 20);
                doc.text('Generated at: ' + new Date().toLocaleString(), 20, 40);
                doc.save('jquery-test.pdf');
                alert('jQuery PDF generated!');
            } else {
                alert('jsPDF not loaded in jQuery handler');
            }
        } catch(e) {
            console.error('jQuery PDF error:', e);
            alert('jQuery Error: ' + e.message);
        }
    });

    // Test if jQuery is working at all
    console.log('jQuery version:', $.fn.jquery);
    console.log('Document ready fired');

    // Add a simple test button click
    setTimeout(function() {
        console.log('🔍 Delayed check - Test PDF button exists:', $('#testPDFBtn').length);
        if ($('#testPDFBtn').length > 0) {
            console.log('✅ Test PDF button found after delay');
        } else {
            console.log('❌ Test PDF button NOT found after delay');
        }
    }, 1000);

    // Add event listeners for view teacher buttons
    $('.view-teacher-btn').on('click', function(e) {
        e.preventDefault();
        const teacherId = $(this).data('teacher-id');
        console.log('View teacher button clicked for ID:', teacherId);

        if (window.openEnhancedTeacherModal) {
            console.log('Calling enhanced modal for teacher:', teacherId);
            window.openEnhancedTeacherModal(teacherId);
        } else {
            console.error('Enhanced modal function not available');
            alert('Enhanced modal not loaded. Please refresh the page.');
        }
    });

    // Add event listeners for modal close buttons
    $('#closeTeacherModalBtn, #closeTeacherModalBtn2').on('click', function() {
        console.log('Close button clicked');
        if (window.closeEnhancedTeacherModal) {
            window.closeEnhancedTeacherModal();
        }
    });

    // Close modal when clicking outside
    $('#teacherModal').on('click', function(e) {
        if (e.target === this) {
            console.log('Clicked outside modal, closing');
            if (window.closeEnhancedTeacherModal) {
                window.closeEnhancedTeacherModal();
            }
        }
    });

    // Check if PDF button exists
    console.log('PDF button exists:', $('#printTeacherProfile').length);
    console.log('Modal footer exists:', $('#teacherModalFooter').length);

    // PDF Generation functionality using event delegation
    $(document).on('click', '#printTeacherProfile', function() {
        console.log('🔄 PDF generation button clicked via event delegation');

        // Show loading state
        const button = $(this);
        const originalText = button.html();
        button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Generating PDF...');
        button.prop('disabled', true);

        // Call our global function
        setTimeout(async () => {
            try {
                if (typeof window.generateTeacherCVPDF === 'function') {
                    await window.generateTeacherCVPDF();

                    // Show success message
                    button.html('<i class="fas fa-check mr-2"></i>PDF Generated!');
                    setTimeout(() => {
                        button.html(originalText);
                        button.prop('disabled', false);
                    }, 2000);
                } else {
                    throw new Error('generateTeacherCVPDF function not available');
                }
            } catch (error) {
                console.error('PDF generation error:', error);
                button.html(originalText);
                button.prop('disabled', false);
            }
        }, 100);
    });

    // Test PDF button functionality using event delegation
    $(document).on('click', '#testPDFBtn', function() {
        console.log('🔄 Test PDF button clicked');

        // Show loading state
        const button = $(this);
        const originalText = button.html();
        button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Testing PDF...');
        button.prop('disabled', true);

        // Check library availability first
        console.log('🔍 Checking PDF libraries...');
        console.log('jsPDF available:', typeof window.jspdf);
        console.log('jsPDF global:', typeof window.jsPDF);
        console.log('PDF generator function:', typeof window.generateTeacherProfilePDF);

        // Create sample teacher data for testing
        const sampleTeacher = {
            name: 'Test Teacher',
            designation: 'Computer Science Teacher',
            department: 'Computer Science',
            employee_id: 'TEST001',
            email: '<EMAIL>',
            phone: '+91-9876543210',
            joining_date: '2020-01-15',
            employment_type: 'Permanent',
            date_of_birth: '1985-05-20',
            gender: 'Male',
            subjects_taught: 'Computer Science, Programming',
            total_experience_years: 8,
            teaching_experience_years: 6,
            address: '123 Test Street',
            city: 'Test City',
            state: 'Test State',
            pincode: '123456'
        };

        setTimeout(() => {
            try {
                // Check if PDF generator is available
                if (typeof window.generateTeacherProfilePDF !== 'function') {
                    throw new Error('PDF generator function not loaded');
                }

                // Check if jsPDF is available
                if (typeof window.jspdf === 'undefined' && typeof window.jsPDF === 'undefined') {
                    throw new Error('jsPDF library not loaded');
                }

                console.log('✅ All checks passed, generating PDF...');

                // Try simple jsPDF test first
                try {
                    let jsPDFClass;
                    if (window.jspdf && window.jspdf.jsPDF) {
                        jsPDFClass = window.jspdf.jsPDF;
                    } else if (window.jsPDF) {
                        jsPDFClass = window.jsPDF;
                    } else {
                        throw new Error('jsPDF not available');
                    }

                    console.log('🔄 Creating simple test PDF...');
                    const doc = new jsPDFClass();
                    doc.text('Test PDF Generation', 20, 20);
                    doc.text('Teacher: ' + sampleTeacher.name, 20, 40);
                    doc.text('Department: ' + sampleTeacher.department, 20, 60);
                    doc.save('test-pdf.pdf');
                    console.log('✅ Simple PDF test successful');

                    // Now try full PDF generation
                    const success = window.generateTeacherProfilePDF(sampleTeacher);

                    if (success) {
                        button.html('<i class="fas fa-check mr-2"></i>PDF Generated!');
                        setTimeout(() => {
                            button.html(originalText);
                            button.prop('disabled', false);
                        }, 2000);
                    } else {
                        throw new Error('PDF generation returned false');
                    }
                } catch (simpleError) {
                    console.error('❌ Simple PDF test failed:', simpleError);
                    // Try the full generator anyway
                    const success = window.generateTeacherProfilePDF(sampleTeacher);

                    if (success) {
                        button.html('<i class="fas fa-check mr-2"></i>PDF Generated!');
                        setTimeout(() => {
                            button.html(originalText);
                            button.prop('disabled', false);
                        }, 2000);
                    } else {
                        throw new Error('PDF generation returned false');
                    }
                }
            } catch (error) {
                console.error('❌ PDF generation error:', error);
                alert(`PDF generation failed: ${error.message}`);
                button.html(originalText);
                button.prop('disabled', false);
            }
        }, 100);
    });

    console.log('Event listeners attached');
});

// Simple PDF generation function that can be called directly
window.generateSimplePDF = function() {
    console.log('🔄 Direct PDF generation called');

    try {
        // Check if jsPDF is available
        let jsPDFClass;
        if (window.jspdf && window.jspdf.jsPDF) {
            jsPDFClass = window.jspdf.jsPDF;
        } else if (window.jsPDF) {
            jsPDFClass = window.jsPDF;
        } else {
            alert('jsPDF library not loaded');
            return false;
        }

        // Create simple PDF
        const doc = new jsPDFClass();
        doc.text('Simple PDF Test', 20, 20);
        doc.text('Generated at: ' + new Date().toLocaleString(), 20, 40);
        doc.text('This confirms PDF generation is working!', 20, 60);
        doc.save('simple-test.pdf');

        console.log('✅ Simple PDF generated successfully');
        return true;
    } catch (error) {
        console.error('❌ Simple PDF generation failed:', error);
        alert('PDF generation failed: ' + error.message);
        return false;
    }
};

// Function to generate teacher PDF directly
window.generateTeacherPDF = function() {
    console.log('🔄 Direct teacher PDF generation called');

    if (!window.currentTeacherData) {
        alert('No teacher data available. Please open a teacher profile first.');
        return false;
    }

    if (typeof window.generateTeacherProfilePDF === 'function') {
        return window.generateTeacherProfilePDF(window.currentTeacherData);
    } else {
        alert('PDF generator not loaded');
        return false;
    }
};

// Search and filter functionality
function refreshTeacherData() {
    console.log('Refreshing teacher data...');
    location.reload();
}

// Search functionality
$(document).on('input', '#search-teachers', function() {
    const searchTerm = $(this).val().toLowerCase();
    $('#teachers-table-body tr').each(function() {
        const name = $(this).data('name') || '';
        const email = $(this).data('email') || '';
        const isVisible = name.includes(searchTerm) || email.includes(searchTerm);
        $(this).toggle(isVisible);
    });
});

// Filter functionality
$(document).on('change', '#filter-profile-completion', function() {
    const filterValue = $(this).val();
    $('#teachers-table-body tr').each(function() {
        const profileCompletion = $(this).data('profile-completion') || '';
        const isVisible = !filterValue || profileCompletion === filterValue;
        $(this).toggle(isVisible);
    });
});

// Send message functionality
function sendMessage(teacherId) {
    console.log('Send message to teacher:', teacherId);
    alert(`Message functionality for teacher ID: ${teacherId} - To be implemented`);
}

// Profile completion calculation function based on actual database columns
function calculateProfileCompletion(teacherData) {
    if (!teacherData) return { percentage: 0, completedFields: 0, totalFields: 35, missingFields: 35 };

    let totalFields = 0;
    let completedFields = 0;

    // Helper function to check if field has meaningful value
    function hasValue(value) {
        return value !== null &&
               value !== undefined &&
               value !== 'null' &&
               value !== 'undefined' &&
               value.toString().trim() !== '' &&
               value.toString().trim() !== '0' &&
               value.toString().trim() !== 'Not provided';
    }

    // Basic user information from users table (8 fields)
    const basicFields = [
        'name', 'email', 'full_name', 'username', 'date_of_birth',
        'bio', 'profile_image', 'subjects'
    ];

    basicFields.forEach(field => {
        totalFields++;
        if (teacherData[field] && hasValue(teacherData[field])) {
            completedFields++;
        }
    });

    // Professional information from staff table (12 fields)
    const professionalFields = [
        'employee_id', 'designation', 'department', 'joining_date',
        'employment_type', 'phone', 'emergency_contact', 'address',
        'office_location', 'current_salary', 'subjects_taught', 'languages_known'
    ];

    professionalFields.forEach(field => {
        totalFields++;
        if (teacherData[field] && hasValue(teacherData[field])) {
            completedFields++;
        }
    });

    // Educational qualifications from staff table (9 fields)
    const educationFields = [
        'class_10_board', 'class_10_year', 'class_10_percentage',
        'class_12_board', 'class_12_year', 'class_12_percentage',
        'graduation_degree', 'graduation_university', 'graduation_year'
    ];

    educationFields.forEach(field => {
        totalFields++;
        if (teacherData[field] && hasValue(teacherData[field])) {
            completedFields++;
        }
    });

    // Experience and additional fields from staff table (6 fields)
    const experienceFields = [
        'total_experience_years', 'teaching_experience_years', 'previous_organizations',
        'special_skills', 'professional_certifications', 'performance_rating'
    ];

    experienceFields.forEach(field => {
        totalFields++;
        if (teacherData[field] && hasValue(teacherData[field])) {
            completedFields++;
        }
    });

    const percentage = totalFields > 0 ? Math.round((completedFields / totalFields) * 100) : 0;

    console.log('Profile completion calculation:', {
        totalFields,
        completedFields,
        percentage,
        teacherData: Object.keys(teacherData)
    });

    return {
        percentage: percentage,
        completedFields: completedFields,
        totalFields: totalFields,
        missingFields: totalFields - completedFields
    };
}

// Update profile completion display in modal
function updateProfileCompletionDisplay(completionData) {
    const percentage = completionData.percentage;
    const completedFields = completionData.completedFields;
    const totalFields = completionData.totalFields;

    // Update summary section only (simplified modal)
    updateProfileCompletionSummary(completionData);
}

// Update detailed profile completion summary
function updateProfileCompletionSummary(completionData) {
    const percentage = completionData.percentage;
    const completedFields = completionData.completedFields;
    const totalFields = completionData.totalFields;

    console.log('📊 Updating profile completion display:', completionData);

    // Update overall completion in summary section
    $('#summary-profile-completion-percentage').text(percentage + '%');

    // Update total and completed field counts
    $('#total-fields-count').text(totalFields);
    $('#completed-fields-count').text(completedFields);

    // Calculate category-wise completion
    const categoryCompletion = calculateCategoryCompletion(window.currentTeacherData);

    // Update basic info completion
    $('#basic-info-completion').text(`${categoryCompletion.basic.completed}/${categoryCompletion.basic.total}`);

    // Update professional completion
    $('#professional-completion').text(`${categoryCompletion.professional.completed}/${categoryCompletion.professional.total}`);

    console.log('✅ Profile completion display updated successfully');
}

// Calculate category-wise completion based on actual database columns
function calculateCategoryCompletion(teacherData) {
    if (!teacherData) return { basic: {completed: 0, total: 8}, professional: {completed: 0, total: 12} };

    // Helper function to check if field has meaningful value
    function hasValue(value) {
        return value !== null &&
               value !== undefined &&
               value !== 'null' &&
               value !== 'undefined' &&
               value.toString().trim() !== '' &&
               value.toString().trim() !== '0' &&
               value.toString().trim() !== 'Not provided';
    }

    // Basic information fields from users table (8 fields)
    const basicFields = ['name', 'email', 'full_name', 'username', 'date_of_birth', 'bio', 'profile_image', 'subjects'];
    let basicCompleted = 0;
    basicFields.forEach(field => {
        if (teacherData[field] && hasValue(teacherData[field])) {
            basicCompleted++;
        }
    });

    // Professional fields from staff table (12 fields)
    const professionalFields = [
        'employee_id', 'designation', 'department', 'joining_date',
        'employment_type', 'phone', 'emergency_contact', 'address',
        'office_location', 'current_salary', 'subjects_taught', 'languages_known'
    ];
    let professionalCompleted = 0;
    professionalFields.forEach(field => {
        if (teacherData[field] && hasValue(teacherData[field])) {
            professionalCompleted++;
        }
    });

    return {
        basic: { completed: basicCompleted, total: basicFields.length },
        professional: { completed: professionalCompleted, total: professionalFields.length }
    };
}

// Function to update table row profile completion when enhanced data is loaded
function updateTableProfileCompletion(teacherId, completionData) {
    console.log('🔄 Updating table profile completion for teacher:', teacherId, completionData);

    const teacherRow = $(`.teacher-row[data-teacher-id="${teacherId}"]`);
    if (teacherRow.length > 0) {
        const progressBar = teacherRow.find('.h-2.rounded-full.bg-blue-500');
        const percentageText = teacherRow.find('.text-sm.font-medium.text-gray-900');

        if (progressBar.length > 0 && percentageText.length > 0) {
            progressBar.css('width', completionData.percentage + '%');
            percentageText.text(completionData.percentage + '%');
            console.log('✅ Updated table row profile completion to:', completionData.percentage + '%');
        }
    }
}

// Make profile completion functions globally available
window.calculateProfileCompletion = calculateProfileCompletion;
window.updateProfileCompletionDisplay = updateProfileCompletionDisplay;
window.calculateCategoryCompletion = calculateCategoryCompletion;
window.updateTableProfileCompletion = updateTableProfileCompletion;

console.log('✅ Profile completion functions made globally available');
</script>

