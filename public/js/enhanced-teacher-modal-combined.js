// Enhanced Teacher Modal - Combined JavaScript
// This file combines all enhanced teacher modal functionality into one optimized file

// Set global flag to prevent conflicts
window.enhancedTeacherModalLoaded = true;
console.log('🚀 Enhanced teacher modal combined JS loaded');

// ===== CORE MODAL FUNCTIONS =====

// Open enhanced teacher modal (globally available)
window.openEnhancedTeacherModal = function(teacherId) {
    console.log('Enhanced modal function called for teacher ID:', teacherId);

    const modal = document.getElementById('teacherModal');
    const loadingDiv = document.getElementById('modal-loading');
    const contentDiv = document.getElementById('enhanced-profile-content');

    if (!modal || !loadingDiv || !contentDiv) {
        console.error('Enhanced modal elements not found');
        alert('Error: Enhanced modal elements not found');
        return;
    }

    // Show modal and loading state
    modal.classList.remove('hidden');
    loadingDiv.classList.remove('hidden');
    contentDiv.classList.add('hidden');

    // Ensure modal footer is visible
    const modalFooter = document.getElementById('teacherModalFooter');
    if (modalFooter) {
        modalFooter.classList.remove('hidden');
        console.log('✅ Modal footer made visible');
    }

    console.log('Fetching enhanced teacher data from API...');

    // Fetch enhanced teacher data
    fetch(`/principal/api/teacher/profile-enhanced?teacher_id=${teacherId}`, {
        method: 'GET',
        credentials: 'include',
        headers: { 'Content-Type': 'application/json' }
    })
    .then(response => {
        console.log('API response status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(result => {
        console.log('🔍 API Response:', result);
        if (result.success) {
            displayEnhancedTeacherProfile(result.teacher);
            loadingDiv.classList.add('hidden');
            contentDiv.classList.remove('hidden');

            // Ensure modal footer is still visible after content load
            if (modalFooter) {
                modalFooter.classList.remove('hidden');
            }

            // Check if PDF button is visible
            const pdfButton = document.getElementById('printTeacherProfile');
            if (pdfButton) {
                console.log('✅ PDF button found and should be visible');
            }
        } else {
            showModalError(result.message || 'Error loading teacher profile');
        }
    })
    .catch(error => {
        console.error('Error fetching teacher profile:', error);
        showModalError(`Failed to load teacher profile: ${error.message}. Please try again.`);
    });
};

// Close enhanced teacher modal (globally available)
window.closeEnhancedTeacherModal = function() {
    const modal = document.getElementById('teacherModal');
    modal.classList.add('hidden');
};

// ===== DISPLAY FUNCTIONS =====

// Display enhanced teacher profile data in modal
function displayEnhancedTeacherProfile(teacher) {
    console.log('🎯 displayEnhancedTeacherProfile called with teacher:', teacher);

    // Store teacher data globally for PDF generation
    window.currentTeacherData = teacher;
    console.log('✅ Teacher data stored globally for PDF generation');

    // Basic information (enhanced with user table data)
    $('#modal-teacher-name').text(teacher.displayName || teacher.fullName || teacher.full_name || teacher.name || 'Unknown Teacher');
    $('#modal-teacher-designation').text(teacher.designation || 'Teacher');
    $('#modal-teacher-department').text(teacher.department || 'Academic Department');
    $('#modal-teacher-employee-id').text(teacher.employee_id || 'N/A');
    $('#modal-teacher-email').text(teacher.primaryEmail || teacher.email || 'No email provided');
    $('#modal-teacher-phone').text(teacher.phone || 'No phone provided');
    $('#modal-teacher-joining-date').text(formatModalDate(teacher.joining_date) || 'Not specified');
    $('#modal-teacher-employment-type').text(capitalizeModalFirst(teacher.employment_type) || 'Not specified');

    // Enhanced personal details with user table integration
    $('#modal-date-of-birth').text(formatModalDate(teacher.dateOfBirth || teacher.date_of_birth) || 'Not provided');
    if (teacher.age) {
        $('#modal-date-of-birth').append(` (Age: ${teacher.age} years)`);
    }
    $('#modal-gender').text(capitalizeModalFirst(teacher.gender) || 'Not specified');
    $('#modal-subjects-taught').text(teacher.subjects_taught || teacher.subjects || 'Not specified');
    $('#modal-classes-handled').text(teacher.classes_handled || 'Not specified');

    // User account information
    $('#modal-account-status').text(teacher.accountStatus || 'Unknown');
    $('#modal-last-login').text(teacher.lastLoginFormatted || 'Never logged in');
    $('#modal-account-created').text(teacher.accountCreated || 'Unknown');
    $('#modal-username').text(teacher.username || 'Not set');

    // Experience stats
    $('#modal-total-experience').text(teacher.total_experience_years || '0');
    $('#modal-teaching-experience').text(teacher.teaching_experience_years || '0');
    $('#modal-administrative-experience').text(teacher.administrative_experience_years || '0');

    // Profile image
    if (teacher.profile_image && teacher.profile_image !== 'null') {
        $('#modal-profile-image').attr('src', teacher.profile_image).removeClass('hidden');
        $('#modal-profile-image-placeholder').addClass('hidden');
    } else {
        const initials = getModalInitials(teacher);
        $('#modal-profile-image-placeholder').text(initials).removeClass('hidden');
    }

    // Display contact and administrative information
    displayModalContactInfo(teacher);
    displayModalAdministrativeInfo(teacher);

    // Display timelines
    displayModalEducationTimeline(teacher.educationTimeline || []);
    displayModalExperienceTimeline(teacher.experienceTimeline || []);

    // Display publications and research
    displayModalPublicationsResearch(teacher);

    // Display enhanced certifications and skills (with fallback to legacy)
    const hasEnhancedCertifications = teacher.certifications && teacher.certifications.length > 0;
    const hasEnhancedSkills = teacher.skillsByCategory && Object.keys(teacher.skillsByCategory).length > 0;

    console.log('🔍 Enhanced data availability:');
    console.log('- Certifications:', hasEnhancedCertifications, teacher.certifications?.length || 0);
    console.log('- Skills:', hasEnhancedSkills, Object.keys(teacher.skillsByCategory || {}).length);

    if (hasEnhancedCertifications) {
        console.log('✅ Using enhanced certifications');
        displayModalEnhancedCertifications(teacher.certifications);
    } else {
        console.log('⚠️ Falling back to legacy certifications');
        displayModalCertifications(teacher);
    }

    if (hasEnhancedSkills) {
        console.log('✅ Using enhanced skills');
        console.log('🔍 Skills data structure:', teacher.skillsByCategory);
        displayModalEnhancedSkills(teacher.skillsByCategory);
    } else {
        console.log('⚠️ Falling back to legacy skills');
        console.log('🔍 Legacy skills data:', { special_skills: teacher.special_skills, languages_known: teacher.languages_known });
        displayModalSkillsAndLanguages(teacher.special_skills, teacher.languages_known);
    }

    // Display achievements - check for enhanced data first
    const hasEnhancedAchievements = teacher.achievementsByCategory && Object.keys(teacher.achievementsByCategory).length > 0;

    if (hasEnhancedAchievements) {
        console.log('✅ Using enhanced achievements');
        displayModalEnhancedAchievements(teacher.achievementsByCategory);
    } else {
        console.log('⚠️ Falling back to legacy achievements');
        displayModalAchievements(teacher.awards_received, teacher.training_programs);
    }

    // Display notes and previous organizations
    displayModalNotesAndOrganizations(teacher);

    // Calculate and display profile completion
    const completionData = calculateEnhancedProfileCompletion(teacher);
    updateEnhancedProfileCompletionDisplay(completionData);
    console.log('✅ Profile completion calculated:', completionData);
}

// ===== UTILITY FUNCTIONS =====

// Modal utility functions
function getModalInitials(teacher) {
    const name = teacher.fullName || teacher.name || teacher.username || 'T';
    return name.split(' ').map(n => n[0]).join('').toUpperCase().substring(0, 2);
}

function formatModalDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

function capitalizeModalFirst(str) {
    if (!str) return '';
    return str.charAt(0).toUpperCase() + str.slice(1);
}

function showModalError(message) {
    const loadingDiv = document.getElementById('modal-loading');
    loadingDiv.innerHTML = `
        <div class="text-center py-12">
            <i class="fas fa-exclamation-triangle text-4xl text-gray-500 mb-4"></i>
            <p class="text-gray-600">${message}</p>
            <button onclick="closeEnhancedTeacherModal()" class="mt-4 px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
                Close
            </button>
        </div>
    `;
}

// ===== COMPATIBILITY FUNCTIONS =====

// Override external JS functions for compatibility
window.openTeacherModal = function(teacherId) {
    console.log('External openTeacherModal called, redirecting to enhanced modal for teacher:', teacherId);
    if (window.openEnhancedTeacherModal) {
        window.openEnhancedTeacherModal(teacherId);
    } else {
        setTimeout(() => window.openEnhancedTeacherModal(teacherId), 100);
    }
};

window.closeTeacherModal = function() {
    console.log('External closeTeacherModal called, redirecting to enhanced modal close');
    if (window.closeEnhancedTeacherModal) {
        window.closeEnhancedTeacherModal();
    }
};

// Send message functionality
window.sendMessage = function(teacherId) {
    console.log('Send message functionality for teacher ID:', teacherId);
    alert(`Message functionality for teacher ID: ${teacherId} - To be implemented`);
};

// ===== TIMELINE DISPLAY FUNCTIONS =====

// Display education timeline in modal (horizontal layout)
function displayModalEducationTimeline(timeline) {
    const container = $('#modal-education-timeline');
    container.empty();

    if (!timeline || timeline.length === 0) {
        container.html('<p class="text-sm text-gray-600">No educational information available</p>');
        return;
    }

    timeline.forEach((item, index) => {
        // Build subjects display if available
        let subjectsHtml = '';
        if (item.subjects) {
            subjectsHtml = '<div class="mt-2"><p class="text-sm font-medium mb-1">Subjects:</p>';
            Object.entries(item.subjects).forEach(([subject, marks]) => {
                subjectsHtml += `<div class="text-sm text-gray-600">• ${subject}: ${marks.marks}/${marks.total}</div>`;
            });
            subjectsHtml += '</div>';
        }

        let educationHtml = `
            <div class="mb-3 text-sm">
                <div class="font-medium">${item.title}</div>
                <div class="text-gray-600">${item.institution}</div>
                ${item.board ? `<div class="text-gray-600">Board: ${item.board}</div>` : ''}
                ${item.specialization ? `<div class="text-gray-600">Specialization: ${item.specialization}</div>` : ''}
                ${item.percentage ? `<div class="text-gray-600">Percentage: ${item.percentage}%</div>` : ''}
                ${item.grade ? `<div class="text-gray-600">Grade: ${item.grade}</div>` : ''}
                ${item.cgpa ? `<div class="text-gray-600">CGPA: ${item.cgpa}</div>` : ''}
                <div class="text-gray-600">Year: ${item.year}</div>
                ${subjectsHtml}
            </div>
        `;

        container.append(educationHtml);
    });
}

// Display experience timeline in modal (horizontal layout - combined professional experience)
function displayModalExperienceTimeline(timeline) {
    console.log('displayModalExperienceTimeline called with:', timeline);
    const container = $('#modal-experience-timeline');
    container.empty();

    if (!timeline || timeline.length === 0) {
        console.log('No experience timeline data - showing empty message');
        container.html('<p class="text-sm text-gray-600">No experience information available</p>');
        return;
    }

    console.log('Processing', timeline.length, 'experience records');

    // Sort timeline by start date to show chronological order
    const sortedTimeline = timeline.sort((a, b) => {
        if (a.year && b.year) return a.year - b.year;
        return 0;
    });

    sortedTimeline.forEach((item, index) => {
        // Build responsibilities display
        let responsibilitiesHtml = '';
        if (item.responsibilities && item.responsibilities.length > 0) {
            responsibilitiesHtml = '<div class="mt-2"><p class="text-sm font-medium mb-1">Key Responsibilities:</p>';
            item.responsibilities.slice(0, 3).forEach(resp => {
                responsibilitiesHtml += `<div class="text-sm text-gray-600 mb-1">• ${resp}</div>`;
            });
            if (item.responsibilities.length > 3) {
                responsibilitiesHtml += `<div class="text-sm text-gray-600">... and ${item.responsibilities.length - 3} more</div>`;
            }
            responsibilitiesHtml += '</div>';
        }

        // Build achievements display
        let achievementsHtml = '';
        if (item.achievements && item.achievements.length > 0) {
            achievementsHtml = '<div class="mt-2"><p class="text-sm font-medium mb-1">Key Achievements:</p>';
            item.achievements.slice(0, 2).forEach(achievement => {
                achievementsHtml += `<div class="text-sm text-gray-600 mb-1">• ${achievement}</div>`;
            });
            if (item.achievements.length > 2) {
                achievementsHtml += `<div class="text-sm text-gray-600">... and ${item.achievements.length - 2} more</div>`;
            }
            achievementsHtml += '</div>';
        }

        // Build skills display
        let skillsHtml = '';
        if (item.skills && item.skills.length > 0) {
            skillsHtml = `<div class="mt-2"><p class="text-sm font-medium mb-1">Skills:</p>`;
            skillsHtml += `<div class="text-sm text-gray-600">${item.skills.slice(0, 4).join(', ')}`;
            if (item.skills.length > 4) {
                skillsHtml += ` +${item.skills.length - 4} more`;
            }
            skillsHtml += '</div></div>';
        }

        let experienceHtml = `
            <div class="mb-3 text-sm">
                <div class="font-medium">${item.title} ${item.isCurrent ? '(Current)' : '(Previous)'}</div>
                <div class="text-gray-600">${item.institution}</div>
                <div class="text-gray-600">Duration: ${item.duration || item.year}</div>
                ${item.description ? `<div class="text-gray-600">Description: ${item.description}</div>` : ''}
                ${item.performanceRating ? `<div class="text-gray-600">Performance: ${item.performanceRating}</div>` : ''}
                ${responsibilitiesHtml}
                ${achievementsHtml}
                ${skillsHtml}
            </div>
        `;

        container.append(experienceHtml);
    });
}

// ===== CONTACT AND ADMINISTRATIVE INFO =====

// Display contact information in modal
function displayModalContactInfo(teacher) {
    $('#modal-alternate-phone').text(teacher.alternate_phone || 'Not provided');
    $('#modal-emergency-contact').text(teacher.emergency_contact || 'Not provided');
    $('#modal-address').text(teacher.address || 'Not provided');
    $('#modal-city').text(teacher.city || 'Not provided');
    $('#modal-state').text(teacher.state || 'Not provided');
    $('#modal-pincode').text(teacher.pincode || 'Not provided');
}

// Display administrative information in modal
function displayModalAdministrativeInfo(teacher) {
    $('#modal-office-location').text(teacher.office_location || 'Not specified');
    $('#modal-confirmation-date').text(formatModalDate(teacher.confirmation_date) || 'Not specified');
    $('#modal-last-promotion').text(formatModalDate(teacher.last_promotion_date) || 'Not specified');
    $('#modal-performance-rating').text(capitalizeModalFirst(teacher.performance_rating) || 'Not rated');
    $('#modal-current-salary').text(teacher.current_salary ? `₹${teacher.current_salary.toLocaleString()}` : 'Not specified');
}

// ===== PUBLICATIONS AND RESEARCH =====

// Display publications and research in modal
function displayModalPublicationsResearch(teacher) {
    // Publications
    const publicationsContainer = $('#modal-publications-list');
    if (teacher.publications) {
        const publicationsArray = teacher.publications.split(',');
        const publicationsHtml = publicationsArray.map(publication =>
            `<div class="text-sm text-gray-600 mb-2">${publication.trim()}</div>`
        ).join('');
        publicationsContainer.html(publicationsHtml);
    } else {
        publicationsContainer.html('<p class="text-sm text-gray-600">No publications available</p>');
    }

    // Research Papers
    const researchContainer = $('#modal-research-papers-list');
    if (teacher.research_papers) {
        const researchArray = teacher.research_papers.split(',');
        const researchHtml = researchArray.map(paper =>
            `<div class="text-sm text-gray-600 mb-2">${paper.trim()}</div>`
        ).join('');
        researchContainer.html(researchHtml);
    } else {
        researchContainer.html('<p class="text-sm text-gray-600">No research papers available</p>');
    }

    // Conferences
    const conferencesContainer = $('#modal-conferences-list');
    if (teacher.conferences_attended) {
        const conferencesArray = teacher.conferences_attended.split(',');
        const conferencesHtml = conferencesArray.map(conference =>
            `<div class="text-sm text-gray-600 mb-2">${conference.trim()}</div>`
        ).join('');
        conferencesContainer.html(conferencesHtml);
    } else {
        conferencesContainer.html('<p class="text-sm text-gray-600">No conferences attended</p>');
    }
}

// ===== LEGACY DISPLAY FUNCTIONS =====

// Display certifications in modal (legacy)
function displayModalCertifications(teacher) {
    // Professional Certifications
    const certificationsContainer = $('#modal-certifications-list');
    if (teacher.professional_certifications) {
        const certificationsArray = teacher.professional_certifications.split(',');
        const certificationsHtml = certificationsArray.map(cert =>
            `<div class="text-sm text-gray-600 mb-2">${cert.trim()}</div>`
        ).join('');
        certificationsContainer.html(certificationsHtml);
    } else {
        certificationsContainer.html('<p class="text-sm text-gray-600">No certifications available</p>');
    }

    // Other Qualifications
    const qualificationsContainer = $('#modal-other-qualifications-list');
    if (teacher.other_qualifications) {
        const qualificationsArray = teacher.other_qualifications.split(',');
        const qualificationsHtml = qualificationsArray.map(qual =>
            `<div class="text-sm text-gray-600 mb-2">${qual.trim()}</div>`
        ).join('');
        qualificationsContainer.html(qualificationsHtml);
    } else {
        qualificationsContainer.html('<p class="text-sm text-gray-600">No additional qualifications available</p>');
    }
}

// Display skills and languages in modal (legacy)
function displayModalSkillsAndLanguages(skills, languages) {
    // Skills
    const skillsContainer = $('#modal-skills-list');
    if (skills) {
        const skillsArray = skills.split(',');
        const skillsHtml = skillsArray.map(skill =>
            `<div class="text-sm text-gray-600 mb-1">${skill.trim()}</div>`
        ).join('');
        skillsContainer.html(skillsHtml);
    } else {
        skillsContainer.html('<p class="text-sm text-gray-600">No skills specified</p>');
    }

    // Languages
    const languagesContainer = $('#modal-languages-list');
    if (languages) {
        const languagesArray = languages.split(',');
        const languagesHtml = languagesArray.map(language =>
            `<div class="text-sm text-gray-600 mb-1">${language.trim()}</div>`
        ).join('');
        languagesContainer.html(languagesHtml);
    } else {
        languagesContainer.html('<p class="text-sm text-gray-600">No languages specified</p>');
    }
}

// Display achievements in modal (legacy)
function displayModalAchievements(awards, training) {
    // Awards
    const awardsContainer = $('#modal-awards-list');
    if (awards) {
        const awardsArray = awards.split(',');
        const awardsHtml = awardsArray.map(award =>
            `<div class="text-sm text-gray-600 mb-2">${award.trim()}</div>`
        ).join('');
        awardsContainer.html(awardsHtml);
    } else {
        awardsContainer.html('<p class="text-sm text-gray-600">No awards specified</p>');
    }

    // Training
    const trainingContainer = $('#modal-training-list');
    if (training) {
        const trainingArray = training.split(',');
        const trainingHtml = trainingArray.map(program =>
            `<div class="text-sm text-gray-600 mb-2">${program.trim()}</div>`
        ).join('');
        trainingContainer.html(trainingHtml);
    } else {
        trainingContainer.html('<p class="text-sm text-gray-600">No training programs specified</p>');
    }
}

// Display notes in modal (enhanced with user bio integration)
function displayModalNotesAndOrganizations(teacher) {
    // Notes - combine user bio and staff notes
    const notesContainer = $('#modal-notes');
    let notesContent = '';

    // Add user bio if available (using userBio field from API)
    if (teacher.userBio && teacher.userBio.trim()) {
        notesContent += `<div class="mb-3">
            <h6 class="text-sm font-medium text-gray-800 mb-2">📝 Profile Bio:</h6>
            <p class="text-gray-700 text-sm leading-relaxed">${teacher.userBio}</p>
        </div>`;
    }

    // Add staff notes if available
    if (teacher.notes && teacher.notes.trim()) {
        notesContent += `<div class="mb-3">
            <h6 class="text-sm font-medium text-gray-800 mb-2">📋 Additional Notes:</h6>
            <p class="text-gray-700 text-sm leading-relaxed">${teacher.notes}</p>
        </div>`;
    }

    // Fallback to bio field if userBio is not available
    if (!notesContent && teacher.bio && teacher.bio.trim()) {
        notesContent += `<div class="mb-3">
            <h6 class="text-sm font-medium text-gray-800 mb-2">📝 Profile Information:</h6>
            <p class="text-gray-700 text-sm leading-relaxed">${teacher.bio}</p>
        </div>`;
    }

    // If no content, show default message
    if (!notesContent) {
        notesContent = '<p class="text-gray-500 text-sm">No additional notes or bio information available</p>';
    }

    notesContainer.html(notesContent);
}

// ===== ENHANCED DISPLAY FUNCTIONS =====

// Display enhanced certifications from API
function displayModalEnhancedCertifications(certifications) {
    console.log('🔍 displayModalEnhancedCertifications called with:', certifications);
    const container = $('#modal-certifications-list');
    if (!container.length) {
        console.log('❌ Certifications container not found');
        return;
    }

    container.empty();
    if (!certifications || certifications.length === 0) {
        container.html('<p class="text-sm text-gray-600">No certifications available</p>');
        return;
    }

    const certificationsHtml = certifications.map(cert => {
        const expiryText = cert.isLifetime ? 'Lifetime' : (cert.expiryDate ? new Date(cert.expiryDate).toLocaleDateString() : 'No expiry');
        const skillsText = cert.skillsCovered && cert.skillsCovered.length > 0 ? cert.skillsCovered.join(', ') : 'No skills listed';

        return `
            <div class="mb-3">
                <div class="text-sm font-medium">${cert.name}</div>
                <div class="text-sm text-gray-600">Issuer: ${cert.issuer}</div>
                <div class="text-sm text-gray-600">Issued: ${new Date(cert.issueDate).toLocaleDateString()}</div>
                <div class="text-sm text-gray-600">Expires: ${expiryText}</div>
                <div class="text-sm text-gray-600">ID: ${cert.certificateId || 'Not provided'}</div>
                <div class="text-sm text-gray-600">Status: ${cert.status}</div>
                ${cert.description ? `<div class="text-sm text-gray-600">Description: ${cert.description}</div>` : ''}
                <div class="text-sm text-gray-600">Skills: ${skillsText}</div>
                <div class="text-sm text-gray-600">Type: ${cert.type}</div>
            </div>
        `;
    }).join('');

    container.html(certificationsHtml);
}

// Display enhanced skills from API
function displayModalEnhancedSkills(skillsByCategory) {
    console.log('🔍 displayModalEnhancedSkills called with:', skillsByCategory);
    const container = $('#modal-skills-list');
    if (!container.length) {
        console.log('❌ Skills container not found');
        return;
    }

    if (!skillsByCategory || Object.keys(skillsByCategory).length === 0) {
        container.html('<p class="text-sm text-gray-600">No skills information available</p>');
        return;
    }

    // Sanitize skills data - handle cases where skills might be objects instead of arrays
    const sanitizedSkills = {};
    Object.entries(skillsByCategory).forEach(([category, skills]) => {
        if (Array.isArray(skills)) {
            sanitizedSkills[category] = skills;
        } else if (skills && typeof skills === 'object') {
            // Convert object to array if needed
            sanitizedSkills[category] = Object.values(skills);
            console.log(`🔧 Converted ${category} skills from object to array`);
        } else {
            console.log(`⚠️ Skipping invalid skills for category ${category}:`, skills);
        }
    });

    if (Object.keys(sanitizedSkills).length === 0) {
        container.html('<p class="text-sm text-gray-600">No valid skills information available</p>');
        return;
    }

    let skillsHtml = '<div class="space-y-4">';

    Object.entries(sanitizedSkills).forEach(([category, skills]) => {
        // Skills should now be arrays after sanitization
        if (!Array.isArray(skills)) {
            console.log(`⚠️ Skills for category ${category} is still not an array after sanitization:`, skills);
            return;
        }

        skillsHtml += `
            <div class="mb-4">
                <h6 class="text-base font-semibold mb-2">
                    ${category.replace('_', ' ')} Skills (${skills.length})
                </h6>
                <div class="space-y-2">
        `;

        skills.forEach(skill => {
            // Handle both object and string skills
            let skillName, skillProficiency, skillExperience, skillCertified, skillLastUsed;

            if (typeof skill === 'object' && skill !== null) {
                skillName = skill.name || skill.skill_name || 'Unknown Skill';
                skillProficiency = skill.proficiency || skill.level || 'Beginner';
                skillExperience = skill.experience || skill.years_experience || 0;
                skillCertified = skill.certified || false;
                skillLastUsed = skill.lastUsed || skill.last_used;
            } else if (typeof skill === 'string') {
                skillName = skill;
                skillProficiency = 'Not specified';
                skillExperience = 0;
                skillCertified = false;
                skillLastUsed = null;
            } else {
                console.log('⚠️ Invalid skill object:', skill);
                return;
            }

            const certifiedBadge = skillCertified ? ' (Certified)' : '';
            skillsHtml += `
                <div class="text-sm">
                    <div class="font-medium">${skillName}</div>
                    <div class="text-gray-600">Proficiency: ${skillProficiency}</div>
                    <div class="text-gray-600">Experience: ${skillExperience} years${certifiedBadge}</div>
                    ${skillLastUsed ? `<div class="text-gray-600">Last used: ${new Date(skillLastUsed).toLocaleDateString()}</div>` : ''}
                </div>
            `;
        });

        skillsHtml += '</div></div>';
    });

    skillsHtml += '</div>';
    container.html(skillsHtml);
}

// Display enhanced achievements from API
function displayModalEnhancedAchievements(achievementsByCategory) {
    console.log('🔍 displayModalEnhancedAchievements called with:', achievementsByCategory);
    const container = $('#modal-achievements-content');
    if (!container.length) {
        console.log('❌ Achievements container not found');
        return;
    }

    if (!achievementsByCategory || Object.keys(achievementsByCategory).length === 0) {
        container.html('<p class="text-sm text-gray-600">No achievements information available</p>');
        return;
    }

    let achievementsHtml = '<div class="space-y-4">';

    // Category configurations without colors
    const categoryConfigs = {
        'software_development': { title: 'Software Development' },
        'teaching_excellence': { title: 'Teaching Excellence' },
        'educational_innovation': { title: 'Educational Innovation' },
        'student_mentoring': { title: 'Student Mentoring' },
        'research_publication': { title: 'Research & Publications' },
        'professional_recognition': { title: 'Professional Recognition' },
        'community_service': { title: 'Community Service' },
        'diversity_inclusion': { title: 'Diversity & Inclusion' },
        'professional_certification': { title: 'Professional Certifications' },
        'entrepreneurship': { title: 'Entrepreneurship' },
        'intellectual_property': { title: 'Intellectual Property' }
    };

    Object.entries(achievementsByCategory).forEach(([category, achievements]) => {
        const config = categoryConfigs[category] || {
            title: category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
        };

        achievementsHtml += `
            <div class="mb-4">
                <h6 class="text-base font-semibold mb-2">
                    ${config.title} (${achievements.length} achievement${achievements.length !== 1 ? 's' : ''})
                </h6>
                <div class="space-y-2">
        `;

        achievements.forEach(achievement => {
            const skillsText = achievement.skills && achievement.skills.length > 0
                ? achievement.skills.slice(0, 3).join(', ') + (achievement.skills.length > 3 ? ` +${achievement.skills.length - 3} more` : '')
                : 'No specific skills listed';

            achievementsHtml += `
                <div class="text-sm">
                    <div class="font-medium">${achievement.title}</div>
                    <div class="text-gray-600">Level: ${achievement.level}</div>
                    <div class="text-gray-600">Description: ${achievement.description}</div>
                    ${achievement.date ? `<div class="text-gray-600">Date: ${new Date(achievement.date).toLocaleDateString()}</div>` : ''}
                    ${achievement.organization ? `<div class="text-gray-600">Organization: ${achievement.organization}</div>` : ''}
                    <div class="text-gray-600">Skills: ${skillsText}</div>
                </div>
            `;
        });

        achievementsHtml += '</div></div>';
    });

    achievementsHtml += '</div>';
    container.html(achievementsHtml);
}

// ===== PDF GENERATION FUNCTIONS =====

// Generate teacher CV PDF using available APIs
window.generateTeacherCVPDF = async function() {
    console.log('🔄 Generating teacher CV PDF...');

    if (!window.currentTeacherData) {
        console.error('❌ No teacher data available');
        alert('No teacher data available. Please open a teacher profile first.');
        return false;
    }

    try {
        const teacherData = window.currentTeacherData;
        console.log('✅ Using teacher data:', teacherData);

        // Try the enhanced PDF API first
        const response = await fetch('/principal/api/generate-enhanced-cv-pdf', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                teacherId: teacherData.id,
                teacherData: teacherData
            })
        });

        const result = await response.json();
        console.log('📄 PDF API response:', result);

        if (result.success) {
            console.log('✅ PDF generated successfully:', result.url);
            // Open the PDF in a new tab
            window.open(result.url, '_blank');
            return true;
        } else {
            throw new Error(result.message || 'PDF generation failed');
        }
    } catch (error) {
        console.error('❌ Enhanced PDF generation failed:', error);
        console.log('🔄 Trying fallback PDF generation...');

        // Fallback to client-side PDF generation
        return generateFallbackPDF();
    }
};

// Fallback PDF generation using client-side jsPDF
function generateFallbackPDF() {
    try {
        console.log('🔄 Using fallback PDF generation...');

        if (!window.currentTeacherData) {
            alert('No teacher data available');
            return false;
        }

        // Check if PDF generator is available
        if (typeof window.generateTeacherProfilePDF === 'function') {
            console.log('✅ Using TeacherProfilePDFGenerator');
            return window.generateTeacherProfilePDF(window.currentTeacherData);
        } else {
            console.log('⚠️ TeacherProfilePDFGenerator not available, using simple PDF');
            return generateSimplePDF();
        }
    } catch (error) {
        console.error('❌ Fallback PDF generation failed:', error);
        alert('PDF generation failed: ' + error.message);
        return false;
    }
}

// Simple PDF generation function
function generateSimplePDF() {
    try {
        console.log('🔄 Generating simple PDF...');

        const teacher = window.currentTeacherData;
        if (!teacher) {
            alert('No teacher data available');
            return false;
        }

        // Check if jsPDF is available
        let jsPDFClass;
        if (window.jspdf && window.jspdf.jsPDF) {
            jsPDFClass = window.jspdf.jsPDF;
        } else if (window.jsPDF) {
            jsPDFClass = window.jsPDF;
        } else {
            console.log('❌ jsPDF not available, using HTML fallback');
            return generateHTMLPDF();
        }

        // Create PDF
        const doc = new jsPDFClass();

        // Add content
        doc.setFontSize(20);
        doc.text(teacher.name || 'Teacher Profile', 20, 30);

        doc.setFontSize(14);
        doc.text(`Designation: ${teacher.designation || 'N/A'}`, 20, 50);
        doc.text(`Department: ${teacher.department || 'N/A'}`, 20, 65);
        doc.text(`Email: ${teacher.email || 'N/A'}`, 20, 80);
        doc.text(`Employee ID: ${teacher.employee_id || 'N/A'}`, 20, 95);

        doc.setFontSize(12);
        doc.text(`Generated on: ${new Date().toLocaleString()}`, 20, 120);

        // Save PDF
        const fileName = `${(teacher.name || 'Teacher').replace(/\s+/g, '_')}_Profile.pdf`;
        doc.save(fileName);

        console.log('✅ Simple PDF generated successfully');
        return true;
    } catch (error) {
        console.error('❌ Simple PDF generation failed:', error);
        return generateHTMLPDF();
    }
}

// HTML-based PDF generation (browser print)
function generateHTMLPDF() {
    try {
        console.log('🔄 Generating HTML PDF...');

        const teacher = window.currentTeacherData;
        if (!teacher) {
            alert('No teacher data available');
            return false;
        }

        const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
            <title>${teacher.name || 'Teacher'} - Profile</title>
            <style>
                @page { size: A4; margin: 20mm; }
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
                h1 { color: #2563eb; margin-bottom: 20px; }
                .info { margin-bottom: 10px; }
                .label { font-weight: bold; color: #374151; }
                @media print { .no-print { display: none; } }
            </style>
        </head>
        <body>
            <div class='no-print' style='background: #f0f0f0; padding: 15px; margin-bottom: 20px; border-radius: 5px;'>
                <h3>Teacher Profile PDF</h3>
                <p>Use <strong>Ctrl+P</strong> or <strong>Cmd+P</strong> to save as PDF</p>
                <button onclick='window.print()' style='background: #2563eb; color: white; border: none; padding: 8px 16px; border-radius: 4px; margin-right: 10px; cursor: pointer;'>Print as PDF</button>
                <button onclick='window.close()' style='background: #666; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;'>Close</button>
            </div>
            <h1>${teacher.name || 'Teacher Profile'}</h1>
            <div class='info'><span class='label'>Designation:</span> ${teacher.designation || 'N/A'}</div>
            <div class='info'><span class='label'>Department:</span> ${teacher.department || 'N/A'}</div>
            <div class='info'><span class='label'>Email:</span> ${teacher.email || 'N/A'}</div>
            <div class='info'><span class='label'>Employee ID:</span> ${teacher.employee_id || 'N/A'}</div>
            <div class='info'><span class='label'>Phone:</span> ${teacher.phone || 'N/A'}</div>
            <div class='info'><span class='label'>Joining Date:</span> ${teacher.joining_date || 'N/A'}</div>
            <div class='info'><span class='label'>Generated:</span> ${new Date().toLocaleString()}</div>
        </body>
        </html>
        `;

        const printWindow = window.open('', '_blank');
        printWindow.document.write(htmlContent);
        printWindow.document.close();

        console.log('✅ HTML PDF window opened');
        return true;
    } catch (error) {
        console.error('❌ HTML PDF generation failed:', error);
        alert('PDF generation failed: ' + error.message);
        return false;
    }
}

// ===== EVENT HANDLERS =====

// Enhanced Teacher Modal Functionality
$(document).ready(function() {
    console.log('Enhanced teacher modal DOM ready');

    // Remove any existing event handlers from external JS
    $('.view-teacher-btn').off('click');
    $('[id^="viewTeacherBtn-"]').off('click');

    // View teacher details button (class-based)
    $(document).on('click', '.view-teacher-btn', function(e) {
        e.preventDefault();
        e.stopPropagation();
        const teacherId = $(this).data('teacher-id');
        openEnhancedTeacherModal(teacherId);
    });

    // View teacher details button (ID-based)
    $(document).on('click', '[id^="viewTeacherBtn-"]', function(e) {
        e.preventDefault();
        e.stopPropagation();
        const teacherId = $(this).data('teacher-id');
        if (!teacherId) {
            // Extract from button ID if data attribute is missing
            const buttonId = $(this).attr('id');
            const extractedId = buttonId.replace('viewTeacherBtn-', '');
            if (extractedId) {
                openEnhancedTeacherModal(extractedId);
                return;
            }
        }
        openEnhancedTeacherModal(teacherId);
    });

    // PDF Generation button handlers for modal
    $(document).on('click', '#printTeacherProfile', function(e) {
        e.preventDefault();
        e.stopPropagation();

        console.log('🔄 Modal PDF generation button clicked');

        const button = $(this);
        const originalHtml = button.html();

        // Show loading state
        button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Generating PDF...');
        button.prop('disabled', true);

        // Generate PDF using current modal data
        setTimeout(async () => {
            try {
                const success = await window.generateTeacherCVPDF();

                if (success) {
                    // Show success state
                    button.html('<i class="fas fa-check mr-2"></i>PDF Generated!');
                    setTimeout(() => {
                        button.html(originalHtml);
                        button.prop('disabled', false);
                    }, 2000);
                } else {
                    throw new Error('PDF generation returned false');
                }
            } catch (error) {
                console.error('❌ PDF generation error:', error);
                button.html('<i class="fas fa-times mr-2"></i>Error');
                setTimeout(() => {
                    button.html(originalHtml);
                    button.prop('disabled', false);
                }, 2000);
            }
        }, 100);
    });

    // PDF Generation button handlers for table rows
    $(document).on('click', '.cv-generate-btn, .generate-cv-btn', function(e) {
        e.preventDefault();
        e.stopPropagation();

        console.log('🔄 Table row PDF generation button clicked');

        const button = $(this);
        const originalHtml = button.html();
        const teacherId = button.data('teacher-id');
        const teacherName = button.data('teacher-name');
        const teacherEmail = button.data('teacher-email');

        if (!teacherId) {
            console.error('❌ No teacher ID found');
            alert('Error: No teacher ID found');
            return;
        }

        // Show loading state
        button.html('<i class="fas fa-spinner fa-spin"></i>');
        button.prop('disabled', true);

        // Generate PDF using teacher ID
        setTimeout(async () => {
            try {
                // First try to get full teacher data from existing API
                let teacherData = null;

                try {
                    // Try the enhanced teacher profile API endpoint with correct URL
                    const response = await fetch(`/principal/api/teacher/profile-enhanced?teacher_id=${teacherId}`);
                    if (response.ok) {
                        const result = await response.json();
                        if (result.success) {
                            teacherData = result.teacher;
                            console.log('✅ Got full enhanced teacher data for PDF');
                        }
                    } else {
                        console.log('⚠️ Enhanced teacher profile API returned:', response.status);

                        // Try the basic teacher profile API as fallback
                        const basicResponse = await fetch(`/principal/api/teacher/profile?teacherId=${teacherId}`);
                        if (basicResponse.ok) {
                            const basicResult = await basicResponse.json();
                            if (basicResult.success) {
                                teacherData = basicResult.teacher;
                                console.log('✅ Got basic teacher data for PDF');
                            }
                        }
                    }
                } catch (error) {
                    console.log('⚠️ Could not get teacher data from API, using basic data:', error.message);
                }

                // Fallback to basic data if full data not available
                if (!teacherData) {
                    teacherData = {
                        id: teacherId,
                        name: teacherName || 'Teacher',
                        email: teacherEmail || '',
                        designation: 'Teacher',
                        department: 'Academic Department'
                    };
                }

                // Store teacher data temporarily
                const previousData = window.currentTeacherData;
                window.currentTeacherData = teacherData;

                // Generate PDF
                const success = await window.generateTeacherCVPDF();

                // Restore previous data
                window.currentTeacherData = previousData;

                if (success) {
                    // Show success state
                    button.html('<i class="fas fa-check"></i>');
                    setTimeout(() => {
                        button.html(originalHtml);
                        button.prop('disabled', false);
                    }, 2000);
                } else {
                    throw new Error('PDF generation returned false');
                }
            } catch (error) {
                console.error('❌ PDF generation error:', error);
                button.html('<i class="fas fa-times"></i>');
                setTimeout(() => {
                    button.html(originalHtml);
                    button.prop('disabled', false);
                }, 2000);
            }
        }, 100);
    });

    // Test PDF button handler
    $(document).on('click', '#testSimplePDF', function(e) {
        e.preventDefault();

        console.log('🔄 Test PDF button clicked');

        const button = $(this);
        const originalHtml = button.html();

        // Show loading state
        button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Testing...');
        button.prop('disabled', true);

        setTimeout(() => {
            try {
                // Create test teacher data
                const testTeacher = {
                    id: 'test',
                    name: 'Test Teacher',
                    designation: 'Computer Science Teacher',
                    department: 'Computer Science',
                    email: '<EMAIL>',
                    employee_id: 'TEST001'
                };

                // Store temporarily
                const previousData = window.currentTeacherData;
                window.currentTeacherData = testTeacher;

                // Generate simple PDF
                const success = generateSimplePDF();

                // Restore previous data
                window.currentTeacherData = previousData;

                if (success) {
                    button.html('<i class="fas fa-check mr-2"></i>PDF Generated!');
                    setTimeout(() => {
                        button.html(originalHtml);
                        button.prop('disabled', false);
                    }, 2000);
                } else {
                    throw new Error('PDF generation failed');
                }
            } catch (error) {
                console.error('❌ Test PDF error:', error);
                button.html('<i class="fas fa-times mr-2"></i>Error');
                setTimeout(() => {
                    button.html(originalHtml);
                    button.prop('disabled', false);
                }, 2000);
            }
        }, 100);
    });

    // Close modal buttons
    $(document).on('click', '#closeTeacherModalBtn, #closeTeacherModalBtn2', function() {
        closeEnhancedTeacherModal();
    });

    // Close modal when clicking outside
    $(document).on('click', '#teacherModal', function(e) {
        if (e.target === this) {
            closeEnhancedTeacherModal();
        }
    });
});

// ===== SEARCH AND FILTER FUNCTIONALITY =====

// Search and filter functionality for teacher management page
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.getElementById('search-teachers');
    const performanceFilter = document.getElementById('filter-performance');
    const teacherRows = document.querySelectorAll('.teacher-row');

    if (searchInput && performanceFilter && teacherRows.length > 0) {
        function filterTeachers() {
            const searchTerm = searchInput.value.toLowerCase();
            const performanceValue = performanceFilter.value;

            teacherRows.forEach(row => {
                const name = row.dataset.name || '';
                const email = row.dataset.email || '';
                const performance = row.dataset.performance || '';

                const matchesSearch = name.includes(searchTerm) || email.includes(searchTerm);
                const matchesPerformance = !performanceValue || performance === performanceValue;

                if (matchesSearch && matchesPerformance) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        searchInput.addEventListener('input', filterTeachers);
        performanceFilter.addEventListener('change', filterTeachers);
    }
});

// ===== UTILITY FUNCTIONS =====

// Refresh teacher data functionality
function refreshTeacherData() {
    console.log('Refreshing teacher data...');
    window.location.reload();
}

// Auto-refresh teacher performance data
function refreshPageData() {
    fetch('/principal/api/teacher-performance')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update the table without full page reload if needed
                console.log('Teacher performance data refreshed');
            }
        })
        .catch(error => {
            console.error('Error refreshing teacher performance:', error);
        });
}

// Start auto-refresh every 5 minutes when page loads
document.addEventListener('DOMContentLoaded', function() {
    if (typeof startAutoRefresh === 'function') {
        startAutoRefresh(300000); // 5 minutes
    }
});

// ===== FINAL INITIALIZATION =====

// Log that the enhanced modal functions are now available
console.log('Enhanced modal functions defined and available:');
console.log('- openEnhancedTeacherModal:', typeof window.openEnhancedTeacherModal);
console.log('- closeEnhancedTeacherModal:', typeof window.closeEnhancedTeacherModal);
console.log('- displayEnhancedTeacherProfile:', typeof displayEnhancedTeacherProfile);

// Test if all required functions are available
setTimeout(() => {
    console.log('🔍 Function availability check:');
    console.log('- displayEnhancedTeacherProfile:', typeof displayEnhancedTeacherProfile);
    console.log('- displayModalEducationTimeline:', typeof displayModalEducationTimeline);
    console.log('- displayModalExperienceTimeline:', typeof displayModalExperienceTimeline);
    console.log('- displayModalEnhancedCertifications:', typeof displayModalEnhancedCertifications);
    console.log('- displayModalEnhancedSkills:', typeof displayModalEnhancedSkills);
}, 1000);

// ===== PROFILE COMPLETION FUNCTIONS =====

// Calculate enhanced profile completion with proper field counting
function calculateEnhancedProfileCompletion(teacher) {
    console.log('🔍 Calculating enhanced profile completion for:', teacher);

    // Helper function to check if field has meaningful value
    function hasValue(value) {
        return value !== null &&
               value !== undefined &&
               value !== 'null' &&
               value !== 'undefined' &&
               value.toString().trim() !== '' &&
               value.toString().trim() !== '0' &&
               value.toString().trim() !== 'Not provided' &&
               value.toString().trim() !== 'N/A' &&
               value.toString().trim() !== '-';
    }

    // Basic profile fields (from users table)
    const basicFields = [
        'name', 'email', 'full_name', 'username', 'date_of_birth',
        'bio', 'profile_image', 'subjects'
    ];

    let basicCompleted = 0;
    basicFields.forEach(field => {
        if (hasValue(teacher[field] || teacher.displayName || teacher.fullName || teacher.primaryEmail)) {
            basicCompleted++;
        }
    });

    // Professional fields (from staff table)
    const professionalFields = [
        'designation', 'department', 'joining_date', 'employment_type',
        'employee_id', 'phone', 'address', 'emergency_contact',
        'qualification', 'specialization', 'experience_years', 'office_location'
    ];

    let professionalCompleted = 0;
    professionalFields.forEach(field => {
        if (hasValue(teacher[field] || teacher.total_experience_years || teacher.teaching_experience_years)) {
            professionalCompleted++;
        }
    });

    // Enhanced data fields
    let enhancedCompleted = 0;
    let enhancedTotal = 0;

    // Count education records
    if (teacher.educationTimeline && teacher.educationTimeline.length > 0) {
        enhancedCompleted++;
    }
    enhancedTotal++;

    // Count experience records
    if (teacher.experienceTimeline && teacher.experienceTimeline.length > 0) {
        enhancedCompleted++;
    }
    enhancedTotal++;

    // Count skills
    if (teacher.skillsByCategory && Object.keys(teacher.skillsByCategory).length > 0) {
        enhancedCompleted++;
    }
    enhancedTotal++;

    // Count certifications
    if (teacher.certifications && teacher.certifications.length > 0) {
        enhancedCompleted++;
    }
    enhancedTotal++;

    // Count achievements
    if (teacher.achievementsByCategory && Object.keys(teacher.achievementsByCategory).length > 0) {
        enhancedCompleted++;
    }
    enhancedTotal++;

    // Calculate totals
    const totalFields = basicFields.length + professionalFields.length + enhancedTotal;
    const totalCompleted = basicCompleted + professionalCompleted + enhancedCompleted;
    const overallPercentage = Math.round((totalCompleted / totalFields) * 100);

    const completionData = {
        overall: overallPercentage,
        basicInfo: {
            completed: basicCompleted,
            total: basicFields.length,
            percentage: Math.round((basicCompleted / basicFields.length) * 100)
        },
        professional: {
            completed: professionalCompleted,
            total: professionalFields.length,
            percentage: Math.round((professionalCompleted / professionalFields.length) * 100)
        },
        enhanced: {
            completed: enhancedCompleted,
            total: enhancedTotal,
            percentage: Math.round((enhancedCompleted / enhancedTotal) * 100)
        },
        totals: {
            completed: totalCompleted,
            total: totalFields
        }
    };

    console.log('📊 Profile completion data:', completionData);
    return completionData;
}

// Update profile completion display with color coding
function updateEnhancedProfileCompletionDisplay(completionData) {
    console.log('🎨 Updating profile completion display with:', completionData);

    // Determine color classes based on completion percentage
    function getCompletionColorClass(percentage) {
        if (percentage >= 80) {
            return 'text-blue-600'; // Complete - Blue
        } else {
            return 'text-gray-600'; // Incomplete - Gray
        }
    }

    // Update overall completion
    const overallElement = $('#summary-profile-completion-percentage');
    if (overallElement.length) {
        overallElement.text(`${completionData.overall}%`);
        overallElement.removeClass('text-blue-600 text-gray-600');
        overallElement.addClass(getCompletionColorClass(completionData.overall));
    }

    // Update basic info completion
    const basicElement = $('#basic-info-completion');
    if (basicElement.length) {
        basicElement.text(`${completionData.basicInfo.completed}/${completionData.basicInfo.total}`);
        basicElement.removeClass('text-blue-600 text-gray-600');
        basicElement.addClass(getCompletionColorClass(completionData.basicInfo.percentage));
    }

    // Update professional completion
    const professionalElement = $('#professional-completion');
    if (professionalElement.length) {
        professionalElement.text(`${completionData.professional.completed}/${completionData.professional.total}`);
        professionalElement.removeClass('text-blue-600 text-gray-600');
        professionalElement.addClass(getCompletionColorClass(completionData.professional.percentage));
    }

    // Update total fields
    const totalFieldsElement = $('#total-fields-count');
    if (totalFieldsElement.length) {
        totalFieldsElement.text(completionData.totals.total);
        totalFieldsElement.removeClass('text-blue-600 text-gray-600');
        totalFieldsElement.addClass(getCompletionColorClass(completionData.overall));
    }

    // Update completed fields
    const completedFieldsElement = $('#completed-fields-count');
    if (completedFieldsElement.length) {
        completedFieldsElement.text(completionData.totals.completed);
        completedFieldsElement.removeClass('text-blue-600 text-gray-600');
        completedFieldsElement.addClass(getCompletionColorClass(completionData.overall));
    }

    console.log('✅ Profile completion display updated');
}

console.log('✅ Enhanced modal combined functions loaded and available');
